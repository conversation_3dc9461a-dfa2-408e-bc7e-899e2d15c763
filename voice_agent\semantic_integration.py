"""
Production-level semantic integration for voice agent system.
Handles semantic caching, query processing, and hospital data management.
Optimized for high-concurrency voice calls with sub-100ms response times.
Enhanced with date-aware caching for time-sensitive healthcare queries.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Tuple, Union, Optional
from shared.redis.migration_helper import get_shared_redis_manager
redis_manager = get_shared_redis_manager()
from .semantic_processor import semantic_processor
from .fuzzy_matcher import fuzzy_matcher
from .language_config import language_config, get_primary_language, get_supported_languages
from .language_utils import (
    contains_indian_script, detect_language_by_script, detect_language_async,
     get_hospital_preferred_languages, set_hospital_language_preferences as set_language_prefs_sync,
    SUPPORTED_INDIC_LANGUAGES
)
# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

# Import date-aware Redis components
try:
    from shared.redis.availability_integration import get_availability_integration
    from shared.redis.voice_agent_date_aware_integration import VoiceAgentDateAwareIntegration
    DATE_AWARE_AVAILABLE = True
    logger.info("Date-aware Redis components loaded successfully")
except ImportError as e:
    logger.warning(f"Date-aware Redis components not available: {e}")
    DATE_AWARE_AVAILABLE = False

class SemanticIntegration:
    """
    Semantic integration layer for voice agent.
    Handles multilingual semantic understanding with IndicBERT support.
    Optimized for 12 Indian languages with enhanced performance.
    Enhanced with date-aware caching for time-sensitive healthcare queries.
    """

    def __init__(self, hospital_id: Optional[str] = None, hospital_timezone: str = "Asia/Kolkata"):
        self.cache_manager = redis_manager
        self.semantic_processor = semantic_processor
        self.fuzzy_matcher = fuzzy_matcher

        # Track language detection confidence for adaptive processing
        self.language_confidence = {}

        # Use shared language definitions
        self.indic_languages = SUPPORTED_INDIC_LANGUAGES

        # Initialize date-aware integration if available
        self.date_aware_integration = None
        self.hospital_id = hospital_id
        self.hospital_timezone = hospital_timezone

        if DATE_AWARE_AVAILABLE and hospital_id:
            try:
                self.date_aware_integration = VoiceAgentDateAwareIntegration(
                    hospital_id=hospital_id,
                    hospital_timezone=hospital_timezone
                )
                logger.info(f"Date-aware integration initialized for hospital {hospital_id}")
            except Exception as e:
                logger.error(f"Failed to initialize date-aware integration: {e}")
                self.date_aware_integration = None
    
    async def process_query(self, query: str, hospital_id: str,
                          language: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process user query with semantic understanding.
        Enhanced with IndicBERT support for better Indian language understanding.
        Now includes date-aware processing for time-sensitive healthcare queries.

        Args:
            query: User query text
            hospital_id: Hospital identifier
            language: Language code (auto-detected if None)
            context: Call context for personalization

        Returns:
            Dict containing response and metadata
        """
        start_time = time.time()

        try:
            # Step 1: Enhanced language detection with hospital context
            language_info = await detect_language_async(query, hospital_id)
            language = await self._validate_language(query, language, hospital_id, language_info)

            # Step 2: Try date-aware processing first if available
            if self.date_aware_integration and hospital_id == self.hospital_id:
                try:
                    date_aware_result = await self.date_aware_integration.process_user_query(
                        query, context
                    )

                    if date_aware_result and date_aware_result.get("success"):
                        # Convert date-aware result to semantic integration format
                        processing_time = (time.time() - start_time) * 1000

                        return {
                            "response": date_aware_result.get("response", ""),
                            "source": "date_aware_cache" if date_aware_result.get("cached") else "date_aware_generated",
                            "confidence": 0.95 if date_aware_result.get("cached") else 0.8,
                            "language": language,
                            "language_name": self.indic_languages.get(language, "Unknown"),
                            "language_detection": language_info,
                            "hospital_id": hospital_id,
                            "processing_time_ms": processing_time,
                            "date_aware": True,
                            "date_context": date_aware_result.get("date_context"),
                            "availability_status": date_aware_result.get("availability", {})
                        }
                except Exception as e:
                    logger.warning(f"Date-aware processing failed, falling back to standard processing: {e}")

            # Step 3: Fallback to standard semantic processor
            result = await self.semantic_processor.process_query(
                query, hospital_id, language, context
            )

            # Step 4: Add enhanced language metadata
            result["language"] = language
            result["language_name"] = self.indic_languages.get(language, "Unknown")
            result["language_detection"] = language_info
            result["hospital_id"] = hospital_id
            result["processing_time_ms"] = (time.time() - start_time) * 1000
            result["date_aware"] = False

            return result
            
        except Exception as e:
            logger.error(f"Error in semantic integration: {e}")
            processing_time = (time.time() - start_time) * 1000
            
            # Get error message in appropriate language
            error_msg = language_config.get_error_message(
                error_type="technical_error",
                language=language or get_primary_language()
            )
            
            return {
                "response": error_msg,
                "source": "error",
                "confidence": 0.0,
                "language": language or get_primary_language(),
                "processing_time_ms": processing_time
            }
    
    async def _validate_language(self, query: str, language: str = None,
                               hospital_id: str = None, language_info: Dict[str, Any] = None) -> str:
        """
        Validate or detect language for query with hospital context.
        Enhanced with shared language utilities and hospital preferences.

        Args:
            query: User query text
            language: Language code (if provided)
            hospital_id: Hospital identifier for context
            language_info: Pre-computed language detection info

        Returns:
            Validated language code
        """
        # If language is provided and valid, use it
        if language and language in get_supported_languages():
            return language

        # Use pre-computed language detection if available
        if language_info and language_info.get('primary_language'):
            detected_lang = language_info['primary_language']
            if detected_lang in get_supported_languages():
                return detected_lang

        # Try hospital-specific language preferences
        if hospital_id:
            preferred_languages = get_hospital_preferred_languages(hospital_id)

            # If query contains Indian script, prefer Indian languages
            if contains_indian_script(query):
                for lang in preferred_languages:
                    if lang != 'en':  # Prefer non-English for Indian scripts
                        return lang
            else:
                # For Latin script, check if English is preferred
                if 'en' in preferred_languages:
                    return 'en'

        # Fallback to script-based detection
        try:
            detected = detect_language_by_script(query)
            if detected and detected in get_supported_languages():
                return detected
        except Exception as e:
            logger.error(f"Script-based language detection error: {e}")

        # Default to primary language
        return get_primary_language()
    
    async def set_hospital_language_preferences(self, hospital_id: str, languages: List[str]) -> bool:
        """
        Set language preferences for a hospital.

        Args:
            hospital_id: Hospital identifier
            languages: List of language codes in order of preference

        Returns:
            Success status
        """
        try:
            # Validate languages
            valid_languages = [lang for lang in languages if lang in get_supported_languages()]
            if not valid_languages:
                logger.warning(f"No valid languages provided for hospital {hospital_id}")
                return False

            # Set preferences using shared utility (sync function)
            set_language_prefs_sync(hospital_id, valid_languages)
            logger.info(f"Set language preferences for hospital {hospital_id}: {valid_languages}")
            return True

        except Exception as e:
            logger.error(f"Error setting language preferences for hospital {hospital_id}: {e}")
            return False

    def get_hospital_language_preferences(self, hospital_id: str) -> List[str]:
        """
        Get language preferences for a hospital.

        Args:
            hospital_id: Hospital identifier

        Returns:
            List of preferred language codes
        """
        return get_hospital_preferred_languages(hospital_id)
    
    async def preload_hospital_data(self, hospital_id: str, hospital_data: Dict[str, Any]) -> bool:
        """
        Preload hospital data for semantic processing.
        Enhanced for multilingual support with IndicBERT.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Hospital data including doctors, tests, etc.

        Returns:
            bool: Success or failure
        """
        try:
            # Get supported languages
            languages = get_supported_languages()
            
            # Preload patterns for each supported language
            tasks = []
            for lang in languages:
                task = self.semantic_processor.preload_common_patterns(
                    hospital_id, hospital_data, lang
                )
                tasks.append(task)
            
            # Run all preload tasks concurrently
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Check for any failures
                success = all(r for r in results if not isinstance(r, Exception))
                if not success:
                    logger.warning(f"Some language preloads failed for hospital {hospital_id}")
                
                return success
            
            return True
            
        except Exception as e:
            logger.error(f"Error preloading hospital data: {e}")
            return False
    
    async def cache_response(self, query: str, response: str, 
                           hospital_id: str, category: str = "general") -> bool:
        """
        Cache response for future semantic matching.
        Uses IndicBERT embeddings for better Indian language matching.

        Args:
            query: User query text
            response: Response to cache
            hospital_id: Hospital identifier
            category: Response category

        Returns:
            bool: Success or failure
        """
        try:
            # Cache response using async method
            result = await self.cache_manager.cache_semantic_response_async(
                query, response, hospital_id, category
            )
            return result
        except Exception as e:
            logger.error(f"Error caching response: {e}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get semantic processing statistics.
        Includes IndicBERT model information.

        Returns:
            Dict with statistics
        """
        try:
            # Get cache stats asynchronously
            cache_stats = await self.cache_manager.get_cache_stats_async()
            
            # Get model information from cache manager
            model_info = {
                "model_name": "ai4bharat/indic-bert",
                "model_type": "ALBERT",
                "languages_supported": list(self.indic_languages.items()),
                "embedding_dimension": 768,  # ALBERT base model dimension
                "performance_optimized": True
            }
            
            # Add date-aware integration stats if available
            stats = {
                "cache_stats": cache_stats,
                "model_info": model_info,
                "supported_languages": self.indic_languages,
                "date_aware_enabled": self.date_aware_integration is not None
            }

            if self.date_aware_integration:
                try:
                    date_aware_stats = self.date_aware_integration.get_integration_stats()
                    stats["date_aware_stats"] = date_aware_stats
                except Exception as e:
                    logger.warning(f"Error getting date-aware stats: {e}")
                    stats["date_aware_stats"] = {"error": str(e)}

            return stats
        except Exception as e:
            logger.error(f"Error getting semantic stats: {e}")
            return {"error": str(e)}

    def initialize_date_aware_integration(self, hospital_id: str, hospital_timezone: str = "Asia/Kolkata") -> bool:
        """
        Initialize date-aware integration for a specific hospital.

        Args:
            hospital_id: Hospital identifier
            hospital_timezone: Hospital timezone

        Returns:
            Success status
        """
        if not DATE_AWARE_AVAILABLE:
            logger.warning("Date-aware components not available")
            return False

        try:
            self.hospital_id = hospital_id
            self.hospital_timezone = hospital_timezone
            self.date_aware_integration = VoiceAgentDateAwareIntegration(
                hospital_id=hospital_id,
                hospital_timezone=hospital_timezone
            )
            logger.info(f"Date-aware integration initialized for hospital {hospital_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize date-aware integration: {e}")
            self.date_aware_integration = None
            return False

    async def sync_availability_data(self, hospital_id: str, availability_data: Dict[str, Any]) -> bool:
        """
        Sync availability data from staff portal to date-aware cache.

        Args:
            hospital_id: Hospital identifier
            availability_data: Availability data from staff portal

        Returns:
            Success status
        """
        if not self.date_aware_integration or hospital_id != self.hospital_id:
            logger.warning(f"Date-aware integration not available for hospital {hospital_id}")
            return False

        try:
            return await self.date_aware_integration.update_availability_from_staff_portal(availability_data)
        except Exception as e:
            logger.error(f"Error syncing availability data: {e}")
            return False

    async def ensure_async_pool_ready(self) -> bool:
        """
        Ensure async Redis pools are ready for semantic processing.

        Returns:
            bool: True if pools are ready
        """
        try:
            # Test the cache manager connection
            if hasattr(self.cache_manager, 'test_connection'):
                return await self.cache_manager.test_connection()

            # Fallback: try to get stats which tests the connection
            await self.get_stats()
            return True
        except Exception as e:
            logger.error(f"Error testing async Redis pools: {e}")
            return False

    async def warm_up_cache(self, hospitals_data: List[Dict[str, Any]]) -> bool:
        """
        Warm up semantic cache with hospital data.

        Args:
            hospitals_data: List of hospital data dictionaries

        Returns:
            bool: True if warm-up successful
        """
        try:
            success_count = 0
            for hospital_data in hospitals_data:
                hospital_id = hospital_data.get('id')
                if hospital_id:
                    success = await self.preload_hospital_data(hospital_id, hospital_data)
                    if success:
                        success_count += 1

            logger.info(f"Cache warm-up completed: {success_count}/{len(hospitals_data)} hospitals loaded")
            return success_count > 0
        except Exception as e:
            logger.error(f"Error warming up semantic cache: {e}")
            return False

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for semantic processing.

        Returns:
            Dict containing performance metrics
        """
        try:
            stats = await self.get_stats()
            return {
                "cache_size": stats.get("cache_entries", 0),
                "hit_rate": stats.get("cache_hit_rate", 0.0),
                "total_queries": stats.get("total_queries", 0),
                "cache_hits": stats.get("cache_hits", 0),
                "cache_misses": stats.get("cache_misses", 0),
                "avg_response_time": stats.get("avg_response_time_ms", 0.0)
            }
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {"error": str(e)}

    async def preload_hospital_cache(self, hospital_id: str, hospital_data: Dict[str, Any]) -> bool:
        """
        Preload cache for a specific hospital (alias for preload_hospital_data).

        Args:
            hospital_id: Hospital identifier
            hospital_data: Hospital data dictionary

        Returns:
            bool: True if preload successful
        """
        return await self.preload_hospital_data(hospital_id, hospital_data)

# Global instance
semantic_integration = SemanticIntegration()

# Convenience functions for easy integration
async def process_query(query: str, hospital_id: str, language: str = None,
                        context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Process query using the semantic engine. Defaults to Hindi (primary language)."""
    return await semantic_integration.process_query(query, hospital_id, language, context)

async def preload_hospital_data(hospital_id: str, hospital_data: Dict[str, Any]) -> bool:
    """Preload data for a hospital."""
    return await semantic_integration.preload_hospital_data(hospital_id, hospital_data)

async def cache_response(query: str, response: str, hospital_id: str, category: str = "general") -> bool:
    """Cache a response for a hospital."""
    return await semantic_integration.cache_response(query, response, hospital_id, category)

async def get_stats() -> Dict[str, Any]:
    """Get semantic processing statistics."""
    return await semantic_integration.get_stats()

async def set_hospital_language_preferences(hospital_id: str, languages: List[str]) -> bool:
    """Set language preferences for a hospital."""
    return await semantic_integration.set_hospital_language_preferences(hospital_id, languages)

def get_hospital_language_preferences(hospital_id: str) -> List[str]:
    """Get language preferences for a hospital."""
    return semantic_integration.get_hospital_language_preferences(hospital_id)

# Date-aware convenience functions
def initialize_date_aware_for_hospital(hospital_id: str, hospital_timezone: str = "Asia/Kolkata") -> bool:
    """Initialize date-aware integration for a specific hospital."""
    return semantic_integration.initialize_date_aware_integration(hospital_id, hospital_timezone)

async def sync_hospital_availability(hospital_id: str, availability_data: Dict[str, Any]) -> bool:
    """Sync availability data from staff portal to date-aware cache."""
    return await semantic_integration.sync_availability_data(hospital_id, availability_data)

def is_date_aware_enabled() -> bool:
    """Check if date-aware processing is available and enabled."""
    return DATE_AWARE_AVAILABLE and semantic_integration.date_aware_integration is not None

def get_date_aware_hospital() -> Optional[str]:
    """Get the hospital ID for which date-aware integration is enabled."""
    return semantic_integration.hospital_id if semantic_integration.date_aware_integration else None
