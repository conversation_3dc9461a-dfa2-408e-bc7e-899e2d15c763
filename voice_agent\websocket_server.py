import asyncio
import logging
from datetime import datetime
import os
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from .websocket_manager import websocket_manager, WebSocketConnection
from .websocket_handlers import message_handler
from .websocket_call_context import websocket_context_manager
from .websocket_response_builder import response_builder

# Module-local logger
logger = logging.getLogger(__name__)

class JambonzWebSocketServer:
    """
    Main WebSocket server for Jambonz integration.
    Coordinates all WebSocket components for production-ready voice agent.
    """
    
    def __init__(self):
        """Initialize WebSocket server"""
        self.manager = websocket_manager
        self.message_handler = message_handler
        self.context_manager = websocket_context_manager
        self.response_builder = response_builder
        
        # Error handling configuration
        self.max_error_rate = float(os.environ.get("WEBSOCKET_MAX_ERROR_RATE", "0.1"))  # 10%
        self.error_window_seconds = int(os.environ.get("WEBSOCKET_ERROR_WINDOW", "60"))  # 1 minute
        
        # Error tracking
        self.error_counts: Dict[str, int] = {}
        self.error_timestamps: Dict[str, list] = {}
        
        # Register message handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register message handlers with the WebSocket manager"""
        self.manager.register_message_handler("session:new", self._handle_session_new)
        self.manager.register_message_handler("verb:hook", self._handle_verb_hook)
        self.manager.register_message_handler("call:status", self._handle_call_status)
        self.manager.register_message_handler("session:reconnect", self._handle_session_reconnect)
        self.manager.register_message_handler("session:redirect", self._handle_session_redirect)
        
        logger.info("Registered WebSocket message handlers")
    
    async def _handle_session_new(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle session:new message with error handling and context management
        
        Args:
            connection: WebSocket connection
            data: Message data
            
        Returns:
            Response message
        """
        try:
            # Extract call information
            call_sid = data.get("call_sid") or data.get("callSid")
            hospital_id = self._extract_hospital_id(data)
            caller_number = data.get("from")
            
            # Create WebSocket call context
            ctx = await self.context_manager.create_context(
                call_id=call_sid,
                hospital_id=hospital_id,
                caller_number=caller_number,
                connection_id=connection.connection_id
            )
            
            # Add message correlation
            message_id = data.get("msgid")
            if message_id:
                await ctx.add_message_correlation(message_id, f"session_new_{call_sid}")
                await ctx.add_pending_ack(message_id)
            
            # Process with message handler
            response = await self.message_handler.handle_session_new(connection, data)
            
            # Remove from pending acks
            if message_id:
                await ctx.remove_pending_ack(message_id)
            
            # Validate response
            if not self.response_builder.validate_response(response):
                logger.error(f"Invalid response generated for session:new")
                return self._create_fallback_response(message_id)
            
            logger.info(f"Successfully handled session:new for call {call_sid}")
            return response
            
        except Exception as e:
            logger.error(f"Error handling session:new: {e}")
            await self._track_error(connection.connection_id, "session_new_error")
            return self._create_error_response(data.get("msgid"), str(e))
    
    async def _handle_verb_hook(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle verb:hook message with error handling
        
        Args:
            connection: WebSocket connection
            data: Message data
            
        Returns:
            Response message
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            message_id = data.get("msgid")
            
            # Get context
            ctx = await self.context_manager.get_context(call_sid)
            if not ctx:
                logger.error(f"No context found for call {call_sid}")
                return self._create_error_response(message_id, "Call context not found")
            
            # Add message correlation
            if message_id:
                await ctx.add_message_correlation(message_id, f"verb_hook_{call_sid}")
                await ctx.add_pending_ack(message_id)
            
            # Process with message handler
            response = await self.message_handler.handle_verb_hook(connection, data)
            
            # Remove from pending acks
            if message_id:
                await ctx.remove_pending_ack(message_id)
            
            # Validate response
            if not self.response_builder.validate_response(response):
                logger.error(f"Invalid response generated for verb:hook")
                return self._create_fallback_response(message_id)
            
            logger.debug(f"Successfully handled verb:hook for call {call_sid}")
            return response
            
        except Exception as e:
            logger.error(f"Error handling verb:hook: {e}")
            await self._track_error(connection.connection_id, "verb_hook_error")
            return self._create_error_response(data.get("msgid"), str(e))
    
    async def _handle_call_status(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Handle call:status message
        
        Args:
            connection: WebSocket connection
            data: Message data
            
        Returns:
            Optional response message
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            call_status = data.get("call_status") or data.get("callStatus")
            
            logger.info(f"Call status update: {call_sid} -> {call_status}")
            
            # Process with message handler
            response = await self.message_handler.handle_call_status(connection, data)
            
            # Clean up context if call ended
            if call_status in ["completed", "failed", "busy", "no-answer"]:
                await self.context_manager.remove_context(call_sid)
                logger.info(f"Cleaned up context for ended call {call_sid}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling call:status: {e}")
            await self._track_error(connection.connection_id, "call_status_error")
            return None
    
    async def _handle_session_reconnect(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle session:reconnect message
        
        Args:
            connection: WebSocket connection
            data: Message data
            
        Returns:
            Response message
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            message_id = data.get("msgid")
            
            # Get existing context
            ctx = await self.context_manager.get_context(call_sid)
            if ctx:
                # Update context for reconnection
                await ctx.handle_reconnection(connection.connection_id)
                
                # Update connection mapping
                self.context_manager.connection_contexts[connection.connection_id] = call_sid
                
                logger.info(f"Handled reconnection for call {call_sid}")
            
            # Process with message handler
            response = await self.message_handler.handle_session_reconnect(connection, data)
            
            # Validate response
            if not self.response_builder.validate_response(response):
                logger.error(f"Invalid response generated for session:reconnect")
                return self._create_fallback_response(message_id)
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling session:reconnect: {e}")
            await self._track_error(connection.connection_id, "session_reconnect_error")
            return self._create_error_response(data.get("msgid"), str(e))
    
    async def _handle_session_redirect(self, connection: WebSocketConnection, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle session:redirect message
        
        Args:
            connection: WebSocket connection
            data: Message data
            
        Returns:
            Response message
        """
        try:
            call_sid = data.get("call_sid") or data.get("callSid")
            message_id = data.get("msgid")
            
            logger.info(f"Session redirect for call {call_sid}")
            
            # For now, treat as session:new
            return await self._handle_session_new(connection, data)
            
        except Exception as e:
            logger.error(f"Error handling session:redirect: {e}")
            await self._track_error(connection.connection_id, "session_redirect_error")
            return self._create_error_response(data.get("msgid"), str(e))
    
    def _extract_hospital_id(self, data: Dict[str, Any]) -> Optional[str]:
        """Extract hospital ID from call data"""
        # Try to extract from called number (DID)
        called_number = data.get("to")
        if called_number:
            from .hospital_utils import extract_hospital_id_from_did
            hospital_id = extract_hospital_id_from_did(called_number)
            if hospital_id:
                return hospital_id
        
        # No fallback - return None to indicate failure
        logger.error(f"Could not extract hospital ID from called number: {called_number}")
        return None
    
    def _create_error_response(self, message_id: Optional[str], error_message: str) -> Dict[str, Any]:
        """Create error response"""
        if message_id:
            return self.response_builder.create_error_response(message_id, error_message)
        else:
            return {
                "type": "jambonz:error",
                "error": error_message,
                "timestamp": datetime.now().isoformat()
            }
    
    def _create_fallback_response(self, message_id: Optional[str]) -> Dict[str, Any]:
        """Create fallback response for invalid responses"""
        verbs = [
            self.response_builder.create_say_verb(
                "I'm sorry, there was a technical issue. Please try your call again later."
            ),
            self.response_builder.create_hangup_verb()
        ]
        
        if message_id:
            return self.response_builder.create_ack_response(message_id, verbs)
        else:
            return self.response_builder.create_command_message(verbs)
    
    async def _track_error(self, connection_id: str, error_type: str):
        """Track errors for monitoring and circuit breaking"""
        import time
        
        current_time = time.time()
        
        # Initialize tracking for connection if needed
        if connection_id not in self.error_counts:
            self.error_counts[connection_id] = 0
            self.error_timestamps[connection_id] = []
        
        # Add error
        self.error_counts[connection_id] += 1
        self.error_timestamps[connection_id].append(current_time)
        
        # Clean old timestamps (outside error window)
        cutoff_time = current_time - self.error_window_seconds
        self.error_timestamps[connection_id] = [
            ts for ts in self.error_timestamps[connection_id] if ts > cutoff_time
        ]
        
        # Check error rate
        recent_errors = len(self.error_timestamps[connection_id])
        if recent_errors > 0:
            error_rate = recent_errors / self.error_window_seconds
            if error_rate > self.max_error_rate:
                logger.warning(f"High error rate detected for connection {connection_id}: {error_rate:.2f} errors/sec")
                # Could implement circuit breaker logic here
        
        logger.debug(f"Tracked error for {connection_id}: {error_type}, recent count: {recent_errors}")
    
    async def start(self):
        """Start the WebSocket server"""
        try:
            await self.manager.start_server()
            logger.info("Jambonz WebSocket server started successfully")
        except Exception as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            raise

    async def serve_forever(self):
        """Start the server and keep it running"""
        await self.start()
        try:
            # Keep the server running by waiting for the server to close
            await self.manager.server.wait_closed()
        except Exception as e:
            logger.error(f"Server error: {e}")
            raise
    
    async def stop(self):
        """Stop the WebSocket server"""
        try:
            await self.manager.stop_server()
            logger.info("Jambonz WebSocket server stopped")
        except Exception as e:
            logger.error(f"Error stopping WebSocket server: {e}")
    
    @asynccontextmanager
    async def lifespan(self):
        """Context manager for server lifecycle"""
        try:
            await self.start()
            yield self
        finally:
            await self.stop()
    
    async def get_server_stats(self) -> Dict[str, Any]:
        """Get comprehensive server statistics"""
        manager_metrics = await self.manager.get_connection_metrics()
        context_stats = self.context_manager.get_stats()
        
        return {
            "websocket_manager": manager_metrics,
            "context_manager": context_stats,
            "error_tracking": {
                "tracked_connections": len(self.error_counts),
                "total_errors": sum(self.error_counts.values())
            }
        }

# Global WebSocket server instance
websocket_server = JambonzWebSocketServer()

if __name__ == "__main__":
    import asyncio

    # This block runs only when the script is executed directly
    # e.g., python -m voice_agent.websocket_server

    async def main():
        """Main coroutine to start and run the server indefinitely."""
        logger.info("Starting WebSocket server as a standalone process...")
        try:
            # This will run forever until the process is stopped
            await websocket_server.serve_forever()
        except (KeyboardInterrupt, asyncio.CancelledError):
            logger.info("Shutdown signal received.")
        finally:
            logger.info("Stopping WebSocket server gracefully...")
            await websocket_server.stop()
            logger.info("WebSocket server has stopped.")

    try:
        # Run the main coroutine
        asyncio.run(main())
    except KeyboardInterrupt:
        # This handles the case where Ctrl+C is pressed before the loop starts
        logger.info("Process interrupted by user.")
