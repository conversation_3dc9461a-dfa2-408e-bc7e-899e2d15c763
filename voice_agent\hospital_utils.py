"""
Hospital utility functions for voice agent.

This module contains utility functions related to hospital configuration,
ID extraction, and selection matching to avoid circular imports.
"""

import asyncio
import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import HTTPException
from .database import get_firestore_db
from .models import HospitalConfig
from .fuzzy_matcher import fuzzy_matcher
from .utils import create_ssh_tunnel, close_ssh_tunnel

logger = logging.getLogger(__name__)

# Keep track of active SSH tunnels (shared with main.py)
active_tunnels = {}


async def get_hospital_config(hospital_id: str) -> HospitalConfig:
    """
    Get hospital configuration from Firestore and establish SSH tunnel if needed
    """
    logger.info(f"Getting hospital configuration for hospital ID: {hospital_id}")
    db = get_firestore_db()
    
    # Path for hospital configuration document is now hospital_{hospital_id}_data
    hospital_doc_id = f'hospital_{hospital_id}_data' # Updated document ID
    logger.info(f"Looking for hospital document at hospitals/{hospital_doc_id}") # Updated logging
    
    try:
        # Get the hospital document from the 'hospitals' collection in a non-blocking way
        loop = asyncio.get_running_loop()
        hospital_doc = await loop.run_in_executor(None, lambda: db.collection('hospitals').document(hospital_doc_id).get())
        
        if hospital_doc.exists:
            logger.info(f"Found hospital config at hospitals/{hospital_doc_id}") # Updated logging
            hospital_data = hospital_doc.to_dict()
            logger.info(f"Hospital data: {hospital_data}")
            
            # If hospital_data doesn't have an 'id' field, add it
            if 'id' not in hospital_data:
                hospital_data['id'] = hospital_id
                
            try:
                config = HospitalConfig(**hospital_data)

                # Check if SSH tunnel is needed and not already active
                # For testing: bypass SSH tunnel if BYPASS_SSH_TUNNEL environment variable is set
                bypass_ssh = os.getenv('BYPASS_SSH_TUNNEL', '').lower() in ('true', '1', 'yes')

                if hasattr(config, 'ssh_tunnel') and config.ssh_tunnel and hospital_id not in active_tunnels and not bypass_ssh:
                    try:
                        # Create SSH tunnel for PostgreSQL connection with robust port management
                        tunnel = create_ssh_tunnel(
                            ssh_host=config.ssh_tunnel.get('host'),
                            ssh_user=config.ssh_tunnel.get('user'),
                            ssh_private_key=config.ssh_tunnel.get('private_key_path'),
                            remote_host='localhost',  # Assume PostgreSQL is on the same server
                            remote_port=5432,
                            hospital_id=hospital_id  # Let port manager handle port allocation
                        )
                        active_tunnels[hospital_id] = tunnel
                        logger.info(f"Established SSH tunnel for hospital {hospital_id}")
                    except Exception as tunnel_err:
                        logger.error(f"Error creating SSH tunnel: {tunnel_err}")
                        # Continue without tunnel

                logger.info(f"Successfully created HospitalConfig for hospital {hospital_id}")
                return config
            except Exception as config_err:
                logger.error(f"Error creating hospital config: {config_err}")
                logger.error(f"Hospital data that failed: {hospital_data}")
                raise HTTPException(status_code=500, detail=f"Invalid hospital configuration: {config_err}")
        else:
            logger.error(f"Hospital document {hospital_doc_id} does not exist in 'hospitals' collection")
    except Exception as e:
        logger.error(f"Error getting hospital config: {e}")

    # If we get here, we couldn't find a valid hospital configuration
    # Updated error message to reflect the new document path format
    logger.error(f"Hospital configuration document 'hospitals/{hospital_doc_id}' not found.")

    # Instead of returning a fake hospital config, raise a proper exception
    # This will be caught by the try/except in the calling functions
    # and return an appropriate error message to the caller
    raise HTTPException(
        status_code=404,
        detail=f"Hospital {hospital_id} configuration (hospitals/{hospital_doc_id}) not found. Please contact support."
    )


def extract_hospital_id_from_did(did: str) -> Optional[str]:
    """
    Extracts the hospital ID from the DID (phone number).
    The hospital ID is assumed to be the last 10 digits of the numeric part of the DID.
    """
    logger.info(f"Attempting to extract hospital ID from DID: {did}")

    if not did:
        logger.warning("DID is None or empty, cannot extract hospital ID.")
        return None

    # Clean the DID to get only numeric characters
    cleaned_did = ''.join(filter(str.isdigit, did))
    logger.info(f"Cleaned DID for hospital ID extraction: {cleaned_did}")

    # Check if the cleaned DID has at least 10 digits
    if len(cleaned_did) >= 10:
        # Take the last 10 digits as the hospital_id
        hospital_id = cleaned_did[-10:]
        logger.info(f"Extracted 10-digit hospital ID: {hospital_id} from DID: {did} (cleaned: {cleaned_did})")
        return hospital_id
    else:
        logger.warning(
            f"Cleaned DID '{cleaned_did}' (from original DID '{did}') is too short "
            f"to extract a 10-digit hospital ID. A minimum of 10 digits is required."
        )
        return None


def match_doctor_selection(speech: str, dtmf: str, doctors: list) -> Optional[dict]:
    """
    Match user input to a doctor from the list
    """
    if not doctors:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            for doctor in doctors:
                if doctor['number'] == selection:
                    return doctor
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    best_match = None
    best_score = 0
    
    for doctor in doctors:
        # Check for doctor name match
        _, name_score = fuzzy_matcher.match(speech, [doctor['name'].lower()])
        # Check for specialty match
        _, specialty_score = fuzzy_matcher.match(speech, [doctor.get('specialty', '').lower()])

        # Use the higher of the two scores
        score = max(name_score, specialty_score)
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = doctor
    
    return best_match


def match_test_selection(speech: str, dtmf: str, tests: list) -> Optional[dict]:
    """
    Match user input to a test from the list
    """
    if not tests:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            for test in tests:
                if test['number'] == selection:
                    return test
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    best_match = None
    best_score = 0
    
    for test in tests:
        _, score = fuzzy_matcher.match(speech, [test['name'].lower()])
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = test
    
    return best_match


def match_time_selection(speech: str, dtmf: str, time_slots: list) -> Optional[str]:
    """
    Match user input to a time slot from the list
    """
    if not time_slots:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            if 1 <= selection <= len(time_slots):
                return time_slots[selection - 1]
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    for slot in time_slots:
        if slot.lower() in speech:
            return slot
    
    # If no exact match, try fuzzy matching
    best_match = None
    best_score = 0
    
    for slot in time_slots:
        _, score = fuzzy_matcher.match(speech, [slot.lower()])
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = slot
    
    return best_match


async def get_available_doctors(hospital_id: str, date: str = None) -> list:
    """
    Get available doctors from Firestore, filtered by availability if date is provided
    """
    logger.info(f"Getting available doctors for hospital ID: {hospital_id}, date: {date}")

    # If no date provided, use today's date
    if not date:
        date = datetime.now().strftime('%Y-%m-%d')

    db = get_firestore_db()
    doctors = []

    # Based on the init_data.py script and Firebase screenshot, we know doctors are stored in:
    # hospital_456_data/doctors/doctors collection
    try:
        # First try the path from the data initialization script
        main_data_collection = f'hospital_{hospital_id}_data'
        doctors_ref = db.collection(main_data_collection).document('doctors').collection('doctors')
        logger.info(f"Looking for doctors at {doctors_ref.path}")

        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, doctors_ref.get)
        doc_count = 0

        for i, doc in enumerate(docs):
            doc_count += 1
            doctor_data = doc.to_dict()
            doctor_data['id'] = doc.id
            doctor_data['number'] = i + 1  # Assign a number for DTMF selection
            doctors.append(doctor_data)

        logger.info(f"Found {doc_count} doctors in {doctors_ref.path}")
    except Exception as e:
        logger.error(f"Error getting doctors from primary path: {e}")

        try:
            # Alternative path: also use hospital_XXX_data structure
            main_data_collection_alt = f'hospital_{hospital_id}_data'
            doctors_ref_alt = db.collection(main_data_collection_alt).document('doctors').collection('doctors')
            logger.info(f"Looking for doctors at alternative path: {doctors_ref_alt.path}")

            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, doctors_ref_alt.get)
            doc_count = 0

            for i, doc in enumerate(docs):
                doc_count += 1
                doctor_data = doc.to_dict()
                doctor_data['id'] = doc.id
                doctor_data['number'] = i + 1  # Assign a number for DTMF selection
                doctors.append(doctor_data)

            logger.info(f"Found {doc_count} doctors in alternative path {doctors_ref_alt.path}")
        except Exception as alt_e:
            logger.error(f"Error getting doctors from alternative path: {alt_e}")

    # If no data was found, log a warning but don't use dummy data
    if not doctors:
        logger.warning(f"No doctors found for hospital {hospital_id} in any collection path. Please check your Firebase data structure.")
        # Return empty list instead of dummy data
        return []

    # Filter doctors by availability if date is provided
    if date:
        try:
            from shared.redis.migration_helper import get_shared_redis_manager
            redis_manager = get_shared_redis_manager()
            available_doctor_ids = await redis_manager.get_available_items_async(hospital_id, 'doctor', date)

            if available_doctor_ids:
                # Filter doctors to only include available ones
                available_doctors = []
                for doctor in doctors:
                    doctor_id = doctor.get('id')
                    # Check if doctor is in available list or if no availability data exists (default to available)
                    if doctor_id in available_doctor_ids or not available_doctor_ids:
                        available_doctors.append(doctor)
                    else:
                        # Check Firebase for availability data as fallback
                        daily_availability = doctor.get('daily_availability', {})
                        is_available = daily_availability.get(date, True)  # Default to available
                        if is_available:
                            available_doctors.append(doctor)

                logger.info(f"Filtered {len(doctors)} doctors to {len(available_doctors)} available on {date}")
                return available_doctors
            else:
                # No Redis data, check Firebase availability
                available_doctors = []
                for doctor in doctors:
                    daily_availability = doctor.get('daily_availability', {})
                    is_available = daily_availability.get(date, True)  # Default to available
                    if is_available:
                        available_doctors.append(doctor)

                logger.info(f"Using Firebase availability: {len(available_doctors)} doctors available on {date}")
                return available_doctors
        except Exception as e:
            logger.error(f"Error filtering doctors by availability: {e}")
            # Return all doctors if filtering fails

    return doctors


async def get_available_tests(hospital_id: str, date: str = None) -> list:
    """
    Get available tests from Firestore, filtered by availability if date is provided
    """
    logger.info(f"Getting available tests for hospital ID: {hospital_id}, date: {date}")

    # If no date provided, use today's date
    if not date:
        date = datetime.now().strftime('%Y-%m-%d')

    db = get_firestore_db()
    tests = []

    # Based on the init_data.py script and screenshots, the tests are stored in:
    # hospital_456_data/test_info/tests collection
    try:
        # First try the path from the data initialization script
        main_data_collection = f'hospital_{hospital_id}_data'
        test_info_doc_ref = db.collection(main_data_collection).document('test_info')

        # First check if there's a tests subcollection
        tests_ref = test_info_doc_ref.collection('tests')
        logger.info(f"Looking for tests at {tests_ref.path}")

        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, tests_ref.get)
        test_count = 0

        for i, doc in enumerate(docs):
            test_count += 1
            test_data = doc.to_dict()
            test_data['id'] = doc.id
            test_data['number'] = i + 1  # Assign a number for DTMF selection
            tests.append(test_data)

        logger.info(f"Found {test_count} tests in {tests_ref.path}")

        # If we don't find any tests in the subcollection, check if they're in the test_info document itself
        if test_count == 0:
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            test_info_doc = await loop.run_in_executor(None, test_info_doc_ref.get)
            if test_info_doc.exists:
                test_info_data = test_info_doc.to_dict()
                logger.info(f"Looking for tests in test_info document: {test_info_data}")

                if 'tests' in test_info_data and isinstance(test_info_data['tests'], list):
                    for i, test in enumerate(test_info_data['tests']):
                        test['id'] = f"test{i+1}" if 'id' not in test else test['id']
                        test['number'] = i + 1
                        tests.append(test)
                    logger.info(f"Found {len(tests)} tests in test_info document")
    except Exception as e:
        logger.error(f"Error getting tests from primary path: {e}")

        try:
            # Alternative path: also use hospital_XXX_data structure
            main_data_collection_alt = f'hospital_{hospital_id}_data'
            tests_ref_alt = db.collection(main_data_collection_alt).document('test_info').collection('tests')
            logger.info(f"Looking for tests at alternative path: {tests_ref_alt.path}")

            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, tests_ref_alt.get)
            test_count = 0

            for i, doc in enumerate(docs):
                test_count += 1
                test_data = doc.to_dict()
                test_data['id'] = doc.id
                test_data['number'] = i + 1  # Assign a number for DTMF selection
                tests.append(test_data)

            logger.info(f"Found {test_count} tests in alternative path {tests_ref_alt.path}")
        except Exception as alt_e:
            logger.error(f"Error getting tests from alternative path: {alt_e}")

    # If no data was found in any collection path, use dummy data for testing
    if not tests:
        logger.warning(f"No tests found for hospital {hospital_id} in any collection path. Please check your Firebase data structure.")
        # Return empty list instead of dummy data
        return []

    # Filter tests by availability if date is provided
    if date:
        try:
            from shared.redis.migration_helper import get_shared_redis_manager
            redis_manager = get_shared_redis_manager()
            available_test_ids = await redis_manager.get_available_items_async(hospital_id, 'test', date)

            if available_test_ids:
                # Filter tests to only include available ones
                available_tests = []
                for test in tests:
                    test_id = test.get('id')
                    # Check if test is in available list or if no availability data exists (default to available)
                    if test_id in available_test_ids or not available_test_ids:
                        available_tests.append(test)
                    else:
                        # Check Firebase for availability data as fallback
                        daily_availability = test.get('daily_availability', {})
                        is_available = daily_availability.get(date, True)  # Default to available
                        if is_available:
                            available_tests.append(test)

                logger.info(f"Filtered {len(tests)} tests to {len(available_tests)} available on {date}")
                return available_tests
            else:
                # No Redis data, check Firebase availability
                available_tests = []
                for test in tests:
                    daily_availability = test.get('daily_availability', {})
                    is_available = daily_availability.get(date, True)  # Default to available
                    if is_available:
                        available_tests.append(test)

                logger.info(f"Using Firebase availability: {len(available_tests)} tests available on {date}")
                return available_tests
        except Exception as e:
            logger.error(f"Error filtering tests by availability: {e}")
            # Return all tests if filtering fails

    return tests


# Wrapper functions to avoid circular imports with main.py
# These functions import from main only when called, not at module level

async def get_available_time_slots(doctor: dict, hospital_id: str) -> list:
    """
    Wrapper for main.get_available_time_slots to avoid circular import
    """
    from . import main
    return await main.get_available_time_slots(doctor, hospital_id)


async def save_appointment(hospital_id: str, patient_name: str, phone: str, doctor_id: str, time: str):
    """
    Wrapper for main.save_appointment to avoid circular import
    """
    from . import main
    return await main.save_appointment(hospital_id, patient_name, phone, doctor_id, time)


async def save_test_booking(hospital_id: str, patient_name: str, phone: str, test_type_id: str, time: str):
    """
    Wrapper for main.save_test_booking to avoid circular import
    """
    from . import main
    return await main.save_test_booking(hospital_id, patient_name, phone, test_type_id, time)


async def send_confirmation_sms(phone: str, appointment_type: str, details: dict, language: str, hospital_id: str):
    """
    Wrapper for main.send_confirmation_sms to avoid circular import
    """
    from . import main
    return await main.send_confirmation_sms(phone, appointment_type, details, language, hospital_id)


async def get_all_hospitals():
    """
    Get all hospitals from Firebase with their basic configuration.
    Production-ready with error handling and dynamic data loading.
    """
    try:
        db = get_firestore_db()

        # Get all hospital documents from the 'hospitals' collection
        loop = asyncio.get_running_loop()
        hospital_docs = await loop.run_in_executor(
            None,
            lambda: list(db.collection('hospitals').stream())
        )

        hospitals_data = []
        for hospital_doc in hospital_docs:
            hospital_config = hospital_doc.to_dict()
            # Extract hospital ID from document ID
            doc_id = hospital_doc.id
            if doc_id.startswith('hospital_') and doc_id.endswith('_data'):
                hospital_id = doc_id.replace('hospital_', '').replace('_data', '')
                hospitals_data.append({
                    "id": hospital_id,
                    "name": hospital_config.get('name', f'Hospital {hospital_id}'),
                    "languages": hospital_config.get('languages', ["hi", "bn", "en"]),
                    "services": hospital_config.get('services', ["doctor_booking", "test_booking"])
                })

        return hospitals_data
    except Exception as e:
        logger.error(f"Error getting all hospitals: {e}")
        return []


async def load_hospitals_for_semantic_cache():
    """
    Load hospital data from Firebase for semantic cache warm-up.
    Returns a list of hospital configurations with doctors and tests data.
    Production-ready with error handling, retry logic, and dynamic data loading.
    """
    max_retries = 3
    base_delay = 2  # seconds

    for attempt in range(max_retries):
        retry_delay = base_delay * (2 ** attempt)  # Exponential backoff

        try:
            db = get_firestore_db()

            # Get all hospital documents from the 'hospitals' collection
            loop = asyncio.get_running_loop()
            hospital_docs = await loop.run_in_executor(
                None,
                lambda: list(db.collection('hospitals').stream())
            )

            hospitals_data = []

            for hospital_doc in hospital_docs:
                try:
                    hospital_config = hospital_doc.to_dict()

                    # Extract hospital ID from document ID
                    doc_id = hospital_doc.id
                    if not (doc_id.startswith('hospital_') and doc_id.endswith('_data')):
                        continue

                    hospital_id = doc_id.replace('hospital_', '').replace('_data', '')

                    # Get hospital languages with fallback
                    hospital_languages = hospital_config.get('languages', ['hi', 'bn', 'en'])

                    # Load doctors data
                    doctors_data = []
                    try:
                        from .database import get_doctors
                        doctors_data = await get_doctors(hospital_id)
                    except Exception as e:
                        logger.warning(f"Could not load doctors for hospital {hospital_id}: {e}")

                    # Load tests data
                    tests_data = []
                    try:
                        from .database import get_tests
                        tests_data = await get_tests(hospital_id)
                    except Exception as e:
                        logger.warning(f"Could not load tests for hospital {hospital_id}: {e}")

                    # Create hospital data structure for semantic cache
                    hospital_data = {
                        "id": hospital_id,
                        "name": hospital_config.get('name', f'Hospital {hospital_id}'),
                        "languages": hospital_languages,
                        "doctors": doctors_data,
                        "tests": tests_data,
                        "settings": hospital_config.get('settings', {}),
                        "address": hospital_config.get('address', ''),
                        "phone": hospital_config.get('phone', ''),
                        "emergency_number": hospital_config.get('emergency_number', '911')
                    }

                    hospitals_data.append(hospital_data)
                    logger.info(f"Successfully loaded hospital {hospital_id} with {len(doctors_data)} doctors and {len(tests_data)} tests")

                except Exception as e:
                    logger.error(f"Error processing hospital document {hospital_doc.id}: {e}")
                    continue

            logger.info(f"Successfully loaded {len(hospitals_data)} hospitals for semantic cache")
            return hospitals_data

        except Exception as e:
            logger.error(f"Attempt {attempt + 1}/{max_retries} failed loading hospitals from Firebase: {e}")
            if attempt == max_retries - 1:
                # Last attempt failed
                logger.error("All attempts failed to load hospitals from Firebase")
                return []
            else:
                # Wait before retry with exponential backoff
                await asyncio.sleep(retry_delay)
                continue

    # This should never be reached, but just in case
    return []
