import os
import json
import logging
from typing import Dict, Any, List, Optional, Tuple

# OpenAI imports for production
import openai
from openai import AsyncOpenAI

from dotenv import load_dotenv
from pathlib import Path
from .language_config import language_config, get_primary_language, get_speech_code

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

# Get the directory containing this file
env_path = Path(__file__).parent / '.env'

# Load environment variables from .env file with explicit path
load_dotenv(dotenv_path=env_path)

# Get OpenAI API key
openai_api_key = os.getenv('OPENAI_API_KEY')
if not openai_api_key:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Initialize async OpenAI client
openai_client = AsyncOpenAI(api_key=openai_api_key)

# Constants for NLP processing - using centralized language config
# Hindi is the primary language for Indian hospitals
def get_language_info(lang_code: str) -> Dict[str, str]:
    """Get language information from centralized config with error handling."""
    try:
        if not lang_code or not isinstance(lang_code, str):
            logger.warning(f"Invalid language code: {lang_code}. Using primary language fallback.")
            lang_code = get_primary_language()

        metadata = language_config.get_language_metadata(lang_code)

        # Validate metadata structure
        if not isinstance(metadata, dict):
            logger.error(f"Invalid metadata type for language {lang_code}: {type(metadata)}. Using fallback.")
            metadata = language_config.get_language_metadata(get_primary_language())

        return {
            'name': metadata.get('english_name', 'Unknown'),
            'code': metadata.get('speech_code', 'hi-IN')
        }
    except Exception as e:
        logger.error(f"Error getting language info for {lang_code}: {e}. Using fallback.")
        # Return safe fallback for Hindi (primary language)
        return {
            'name': 'Hindi',
            'code': 'hi-IN'
        }

# Dynamic language mapping based on supported languages with error handling
try:
    supported_languages = language_config.get_supported_languages()

    # Validate supported languages list
    if not supported_languages or not isinstance(supported_languages, list):
        logger.error(f"Invalid supported languages: {supported_languages}. Using default fallback.")
        supported_languages = [get_primary_language(), 'en']  # Hindi and English as fallback

    # Filter out None/empty values and build language mapping
    LANGUAGES = {
        lang_code: get_language_info(lang_code)
        for lang_code in supported_languages
        if lang_code  # Filter out None/empty values
    }

    # Ensure at least one language is available
    if not LANGUAGES:
        logger.error("No supported languages found in language configuration. Adding fallback languages.")
        # Add fallback languages manually
        LANGUAGES = {
            get_primary_language(): get_language_info(get_primary_language()),
            'en': get_language_info('en')
        }

    logger.info(f"Initialized language support for: {list(LANGUAGES.keys())}")

except Exception as e:
    logger.error(f"Critical error initializing language configuration: {e}. Using minimal fallback.")
    # Minimal fallback configuration
    LANGUAGES = {
        'hi': {'name': 'Hindi', 'code': 'hi-IN'},
        'bn': {'name': 'Bengali', 'code': 'bn-IN'},
        'en': {'name': 'English', 'code': 'en-IN'}
    }

# Ensure at least one language is available (final safety check)
if not LANGUAGES:
    raise ValueError("No supported languages found in language configuration")

# TODO: When scaling to more languages, they will be automatically included
# from the language_config.py file without code changes here

class NLPProcessor:
    def __init__(self):
        # Using GPT-4o Mini (GPT-4.1 mini) for cost efficiency and performance
        self.model = "gpt-4.1-mini"  # Changed it properly
        self.max_tokens = 200
        self.temperature = 0.3  # Lower temperature for more deterministic responses
    
    async def process_speech(self, text: str, language: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process speech input using LLM model with context
        """
        try:
            if not text or text.strip() == "":
                return {"understood": False, "response": "I didn't catch what you said."}

            # Prepare the prompt based on the current state and context
            prompt = self._create_prompt(text, language, context)

            # Call async OpenAI API directly
            response = await openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": prompt["system"]},
                    {"role": "user", "content": prompt["user"]}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                response_format={"type": "json_object"}
            )

            # Extract and parse response
            result_text = response.choices[0].message.content
            try:
                result = json.loads(result_text)
                # Add some default values if not present
                if "understood" not in result:
                    result["understood"] = True
                return result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse JSON response: {result_text}")
                return {
                    "understood": False,
                    "response": language_config.get_error_message(
                        error_type="technical_error",
                        language=language or get_primary_language()
                    )
                }

        except Exception as e:
            logger.error(f"Error processing speech: {e}")
            return {
                "understood": False,
                "response": language_config.get_error_message(
                    error_type="technical_error",
                    language=language or get_primary_language()
                )
            }
    
    def _create_prompt(self, text: str, language: str, context: Dict[str, Any]) -> Dict[str, str]:
        """
        Create appropriate prompts based on the context state
        """
        state = context.get("state", "greeting")
        hospital_name = context.get("hospital_name", "the hospital")
        
        # Common system prompt instructions with safe language fallback
        try:
            # Safe language name lookup with multiple fallbacks
            lang_info = LANGUAGES.get(language)
            if not lang_info:
                # Try primary language as fallback
                primary_lang = get_primary_language()
                lang_info = LANGUAGES.get(primary_lang)
                if not lang_info:
                    # Final fallback to any available language
                    lang_info = next(iter(LANGUAGES.values()), {'name': 'Unknown'})

            language_name = lang_info.get('name', 'Unknown')
        except Exception as e:
            logger.warning(f"Error getting language name for {language}: {e}. Using fallback.")
            language_name = 'Unknown'

        system_prompt = (
            "You are Megha, a medical voice agent for Ascle AI that helps patients book "
            "appointments and medical tests at hospitals. "
            f"This call is with a patient speaking {language_name}. "
            "Provide clear, concise responses focused only on appointment booking.\n\n"
            "Respond with a JSON object that includes:\n"
            "- 'understood': Boolean if you understood the request\n"
            "- 'response': Text to speak back to the user\n"
            "- 'intent': Detected intent (if applicable)\n"
            "- Additional fields based on context"
        )
        
        # State-specific prompts
        if state == "greeting":
            system_prompt += (
                "\n\nThe user is selecting a language from the supported list. "
                "Detect which language the user is choosing. "
                "The supported language codes are: " + ", ".join(LANGUAGES.keys()) + ". "
                "Add 'language_choice' field with the detected language code (e.g., 'en', 'hi', 'ta')."
            )
            
        elif state == "main_menu":
            system_prompt += (
                "\n\nThe user is choosing between booking a doctor appointment (option 1) "
                "or a medical test (option 2). "
                "Add 'menu_selection' field with the value 1 or 2."
            )
            
        elif state == "doctor_booking":
            system_prompt += (
                "\n\nThe user is selecting a doctor by name or specialty. "
                "Extract the doctor name or specialty mentioned. "
                "Add 'doctor_selection' field with the extracted information."
            )
            
        elif state == "test_booking":
            system_prompt += (
                "\n\nThe user is selecting a medical test. "
                "Extract the test name mentioned. "
                "Add 'test_selection' field with the extracted information."
            )
            
        elif state == "appointment_time" or state == "test_time":
            system_prompt += (
                "\n\nThe user is selecting a time slot. "
                "Extract the time or time slot number mentioned. "
                "Add 'time_selection' field with the extracted information."
            )
            
        elif state == "patient_name":
            system_prompt += (
                "\n\nThe user is providing their name. "
                "Extract the full name mentioned. "
                "Add 'patient_name' field with the extracted information."
            )
            
        elif state == "confirmation":
            system_prompt += (
                "\n\nThe user is confirming or rejecting a booking. "
                "Determine if the response is positive (yes/confirm) or negative (no/cancel). "
                "Add 'confirmed' field with true or false."
            )
        
        # User prompt with the actual speech text
        user_prompt = f"User said: \"{text}\""
        
        return {
            "system": system_prompt,
            "user": user_prompt
        }
    
    def fuzzy_match(self, input_text: str, options: List[Dict[str, Any]], key: str = "name") -> Tuple[Optional[Dict[str, Any]], float]:
        """
        Find the best fuzzy match between input text and a list of options
        Returns the best matching option and the match score (0-1)
        """
        if not input_text or not options:
            return None, 0.0
        
        input_words = set(input_text.lower().split())
        
        best_match = None
        best_score = 0.0
        
        for option in options:
            option_text = option.get(key, "").lower()
            option_words = set(option_text.split())
            
            # Skip empty options
            if not option_words:
                continue
            
            # Calculate similarity score (Jaccard similarity)
            intersection = len(input_words.intersection(option_words))
            union = len(input_words.union(option_words))
            
            score = intersection / union if union > 0 else 0.0
            
            # Check if this is the best match so far
            if score > best_score:
                best_score = score
                best_match = option
        
        return best_match, best_score
    
    async def extract_entities(self, text: str, entity_type: str) -> List[str]:
        """
        Extract entities of specified type from text using LLM
        """
        try:
            system_prompt = (
                f"Extract all {entity_type} mentioned in the text. "
                f"Return a JSON array of extracted {entity_type}s. "
                "If none found, return an empty array."
            )

            # Call async OpenAI API directly
            response = await openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text}
                ],
                max_tokens=100,
                temperature=0.3,
                response_format={"type": "json_object"}
            )

            result_text = response.choices[0].message.content
            result = json.loads(result_text)

            # Handle both cases: bare array or object with entity field
            if isinstance(result, list):
                return result
            entity_field = f"{entity_type}s"
            return result.get(entity_field, [])

        except Exception as e:
            logger.error(f"Error extracting {entity_type}s: {e}")
            return []

    async def create_medical_response(self, query: str, language: str = None) -> str:
        """
        Generate a medical field-related response to a patient query.
        Defaults to Hindi (primary language) if no language specified.
        """
        # Default to Hindi (primary language) if not specified
        if not language:
            language = get_primary_language()

        try:
            # Get language-specific information with safe fallback
            lang_info = LANGUAGES.get(language)
            if not lang_info:
                # Try primary language as fallback
                primary_lang = get_primary_language()
                lang_info = LANGUAGES.get(primary_lang)
                if not lang_info:
                    # Final fallback to any available language
                    lang_info = next(iter(LANGUAGES.values()), {'name': 'Hindi'})

            lang_name = lang_info.get('name', 'Hindi')

            system_prompt = (
                f"You are Megha, a medical voice assistant for Indian hospitals. "
                f"Respond in {lang_name} language. Provide brief, accurate responses to "
                "medical questions. Only answer questions related to medical topics, appointments, "
                "or hospital services. For non-medical questions, politely redirect to medical topics. "
                "Keep responses under 100 words, focused on factual medical information."
            )

            # Call async OpenAI API directly
            response = await openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": query}
                ],
                max_tokens=150,
                temperature=0.3
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error creating medical response: {e}")
            return language_config.get_error_message(
                error_type="technical_error",
                language=language or get_primary_language()
            )

# Initialize global NLP processor instance
nlp_processor = NLPProcessor()

async def process_speech(text: str, language: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process speech input using the global NLP processor
    """
    return await nlp_processor.process_speech(text, language, context)

def fuzzy_match(input_text: str, options: List[Dict[str, Any]], key: str = "name") -> Tuple[Optional[Dict[str, Any]], float]:
    """
    Find the best fuzzy match between input text and a list of options
    """
    return nlp_processor.fuzzy_match(input_text, options, key)

async def extract_entities(text: str, entity_type: str) -> List[str]:
    """
    Extract entities of specified type from text
    """
    return await nlp_processor.extract_entities(text, entity_type)

async def create_medical_response(query: str, language: str = None) -> str:
    """
    Generate a medical field-related response to a patient query
    """
    return await nlp_processor.create_medical_response(query, language)
