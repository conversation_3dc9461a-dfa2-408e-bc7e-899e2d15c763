import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from .call_context import CallContext
from .language_config import language_config, get_primary_language, detect_language, get_language_name
from .language_utils import (
    contains_indian_script, detect_language_async, detect_language_by_script,
    get_hospital_preferred_languages, get_script_confidence
)
from .database import get_doctors, get_tests, create_appointment, create_test_booking
from .appointment_scheduler import appointment_scheduler
from .hospital_utils import get_hospital_config, extract_hospital_id_from_did, match_doctor_selection, match_test_selection, match_time_selection
from .fuzzy_matcher import match_doctor_by_name, match_test_by_name, match_time_slot
from .utils import format_appointment_datetime, validate_phone_number

# Module-local logger
logger = logging.getLogger(__name__)

class WebSocketStateHandlers:
    """
    Handles state-specific logic for WebSocket voice interactions.
    Mirrors the state machine logic from main.py but adapted for WebSocket responses.
    """
    
    async def handle_language_selection(self, ctx: CallContext, speech_result: str,
                                      dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle language selection in greeting state"""
        try:
            # Get hospital configuration for supported languages
            hospital_config = await get_hospital_config(hospital_id)

            # Get hospital-specific language preferences
            available_languages = get_hospital_preferred_languages(hospital_id)
            if not available_languages:
                available_languages = getattr(hospital_config, 'languages', None)
                if not available_languages:
                    available_languages = language_config.get_supported_languages()

            # Ensure Hindi is first if available (primary language preference)
            if "hi" in available_languages and available_languages[0] != "hi":
                available_languages = ["hi"] + [lang for lang in available_languages if lang != "hi"]

            # Enhanced language selection with script detection
            selected_language = await self._determine_language_selection_enhanced(
                speech_result, dtmf_digits, available_languages, hospital_id
            )
            
            if selected_language in available_languages:
                await ctx.set_language(selected_language)
                await ctx.update_state("main_menu")
                
                # Translate main menu options
                main_menu_text = await self._get_translated_text(
                    "Please say 1 or press 1 to book a doctor's appointment. Say 2 or press 2 to book a medical test.",
                    selected_language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": main_menu_text
                    }
                }]
            else:
                # Language not supported, retry
                languages_text = ", ".join([get_language_name(lang) for lang in available_languages])
                retry_text = f"Sorry, we support {languages_text}. Please select one."

                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]
                
        except Exception as e:
            logger.error(f"Error in language selection: {e}")
            return self._create_error_verbs("Language selection failed")
    
    async def handle_main_menu(self, ctx: CallContext, speech_result: str,
                             dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle main menu selection"""
        try:
            selected_option = self._determine_menu_selection(speech_result, dtmf_digits)
            
            if selected_option == 1:  # Book doctor appointment
                await ctx.update_state("doctor_booking")
                
                # Get available doctors
                today = datetime.now().strftime('%Y-%m-%d')
                doctors = await self._get_available_doctors(hospital_id, today)
                doctor_options = await self._format_doctor_options(doctors, ctx.language)
                
                prompt_text = await self._get_translated_text(
                    "Please select a doctor by saying their name or specialty, or press the corresponding number.",
                    ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {doctor_options}"
                    }
                }]

            elif selected_option == 2:  # Book test
                await ctx.update_state("test_booking")

                # Get available tests
                today = datetime.now().strftime('%Y-%m-%d')
                tests = await self._get_available_tests(hospital_id, today)
                test_options = await self._format_test_options(tests, ctx.language)

                prompt_text = await self._get_translated_text(
                    "Please select a test by saying the test name or press the corresponding number.",
                    ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {test_options}"
                    }
                }]
            else:
                # Invalid option, retry
                retry_text = await self._get_translated_text(
                    "Sorry, I didn't understand. Please say 1 for doctor appointment or 2 for medical test.",
                    ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]
                
        except Exception as e:
            logger.error(f"Error in main menu handling: {e}")
            return self._create_error_verbs("Menu selection failed")
    
    async def handle_doctor_booking(self, ctx: CallContext, speech_result: str,
                                  dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle doctor selection"""
        try:
            # Get available doctors
            today = datetime.now().strftime('%Y-%m-%d')
            doctors = await self._get_available_doctors(hospital_id, today)
            selected_doctor = self._match_doctor_selection(speech_result, dtmf_digits, doctors)
            
            if selected_doctor:
                await ctx.update_data({
                    "appointment_type": "doctor",
                    "doctor_id": selected_doctor["id"],
                    "doctor_name": selected_doctor["name"]
                })
                await ctx.update_state("appointment_time")
                
                # Get available time slots
                time_slots = await self._get_available_time_slots(selected_doctor, hospital_id)
                slot_options = await self._format_time_slots(time_slots, ctx.language)
                
                prompt_text = await self._get_translated_text(
                    f"Please select an appointment time with Dr. {selected_doctor['name']}.",
                    ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {slot_options}"
                    }
                }]
            else:
                # Doctor not found, retry
                retry_text = await self._get_translated_text(
                    "Sorry, I couldn't find that doctor. Please try again.",
                    ctx.language
                )

                doctor_options = await self._format_doctor_options(doctors, ctx.language)

                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{retry_text} {doctor_options}"
                    }
                }]
                
        except Exception as e:
            logger.error(f"Error in doctor booking: {e}")
            return self._create_error_verbs("Doctor booking failed")
    
    async def handle_test_booking(self, ctx: CallContext, speech_result: str,
                                dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle test selection"""
        try:
            # Get available tests
            today = datetime.now().strftime('%Y-%m-%d')
            tests = await self._get_available_tests(hospital_id, today)
            selected_test = self._match_test_selection(speech_result, dtmf_digits, tests)
            
            if selected_test:
                await ctx.update_data({
                    "appointment_type": "test",
                    "test_id": selected_test["id"],
                    "test_name": selected_test["name"]
                })
                await ctx.update_state("test_time")
                
                # Get available time slots for tests
                time_slots = await self._get_available_test_slots(hospital_id, selected_test["id"])
                slot_options = await self._format_time_slots(time_slots, ctx.language)
                
                prompt_text = await self._get_translated_text(
                    f"Please select a time for your {selected_test['name']} test.",
                    ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {slot_options}"
                    }
                }]
            else:
                # Test not found, retry
                retry_text = await self._get_translated_text(
                    "Sorry, I couldn't find that test. Please try again.",
                    ctx.language
                )
                
                test_options = await self._format_test_options(tests, ctx.language)
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{retry_text} {test_options}"
                    }
                }]
                
        except Exception as e:
            logger.error(f"Error in test booking: {e}")
            return self._create_error_verbs("Test booking failed")
    
    async def handle_time_selection(self, ctx: CallContext, speech_result: str,
                                  dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle time slot selection"""
        try:
            is_test = (ctx.state == "test_time")
            
            # Get available time slots
            if is_test:
                test_id = ctx.data.get("test_id")
                time_slots = await self._get_available_test_slots(hospital_id, test_id)
            else:
                doctor_id = ctx.data.get("doctor_id")
                doctor_data = {"id": doctor_id, "schedule": {}}
                time_slots = await self._get_available_time_slots(doctor_data, hospital_id)
            
            selected_time = self._match_time_selection(speech_result, dtmf_digits, time_slots)
            
            if selected_time:
                await ctx.update_data({
                    "appointment_time": selected_time,
                    "time": selected_time  # Keep for backward compatibility
                })
                await ctx.update_state("patient_name")
                
                prompt_text = await self._get_translated_text(
                    "Please say your full name.",
                    ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": prompt_text
                    }
                }]
            else:
                # Time not found, retry
                retry_text = await self._get_translated_text(
                    "Sorry, I couldn't understand that time. Please try again.",
                    ctx.language
                )
                
                slot_options = await self._format_time_slots(time_slots, ctx.language)
                
                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{retry_text} {slot_options}"
                    }
                }]
                
        except Exception as e:
            logger.error(f"Error in time selection: {e}")
            return self._create_error_verbs("Time selection failed")

    async def handle_patient_name(self, ctx: CallContext, speech_result: str,
                                dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle patient name collection"""
        try:
            if speech_result and len(speech_result.strip()) > 2:
                await ctx.update_data({"patient_name": speech_result.strip()})
                await ctx.update_state("confirmation")

                # Create confirmation message
                booking_type = "appointment" if ctx.data.get("doctor_id") else "test"
                provider_name = ctx.data.get("doctor_name") or ctx.data.get("test_name")
                time_slot = ctx.data.get("time")
                patient_name = ctx.data.get("patient_name")

                confirmation_text = await self._get_translated_text(
                    f"Please confirm: {booking_type} for {patient_name} with {provider_name} at {time_slot}. Say 'yes' to confirm or 'no' to cancel.",
                    ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": confirmation_text
                    }
                }]
            else:
                # Name too short, retry
                retry_text = await self._get_translated_text(
                    "Please say your full name clearly.",
                    ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "websocket",  # WebSocket-only mode
                    "input": ["speech"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]

        except Exception as e:
            logger.error(f"Error in patient name handling: {e}")
            return self._create_error_verbs("Name collection failed")

    async def handle_confirmation(self, ctx: CallContext, speech_result: str,
                                dtmf_digits: str, hospital_id: str) -> List[Dict[str, Any]]:
        """Handle booking confirmation"""
        try:
            confirmation = self._determine_confirmation(speech_result, dtmf_digits)

            if confirmation:
                # Confirmed - process booking
                booking_result = await self._process_booking(ctx, hospital_id)

                if booking_result["success"]:
                    success_text = await self._get_translated_text(
                        f"Your booking has been confirmed. Reference number: {booking_result.get('reference', 'N/A')}. Thank you for calling.",
                        ctx.language
                    )

                    return [{
                        "verb": "say",
                        "text": success_text
                    }, {
                        "verb": "hangup"
                    }]
                else:
                    error_text = await self._get_translated_text(
                        f"Sorry, there was an error processing your booking: {booking_result.get('error', 'Unknown error')}. Please try again.",
                        ctx.language
                    )

                    return [{
                        "verb": "say",
                        "text": error_text
                    }, {
                        "verb": "hangup"
                    }]
            else:
                # Cancelled
                cancel_text = await self._get_translated_text(
                    "Your booking has been cancelled. Thank you for calling.",
                    ctx.language
                )

                return [{
                    "verb": "say",
                    "text": cancel_text
                }, {
                    "verb": "hangup"
                }]

        except Exception as e:
            logger.error(f"Error in confirmation handling: {e}")
            return self._create_error_verbs("Confirmation failed")

    # Utility methods
    async def _determine_language_selection_enhanced(self, speech_result: str, dtmf_digits: str,
                                                   available_languages: List[str], hospital_id: str) -> str:
        """Enhanced language selection with script detection and confidence scoring"""
        default_language = get_primary_language()
        if default_language not in available_languages:
            default_language = available_languages[0] if available_languages else "hi"

        # Check DTMF first (highest priority)
        if dtmf_digits and dtmf_digits.isdigit():
            digit = int(dtmf_digits)
            if 1 <= digit <= len(available_languages):
                return available_languages[digit-1]

        # Enhanced speech recognition with script detection
        if speech_result:
            try:
                # Use async language detection for better accuracy
                language_info = await detect_language_async(speech_result, hospital_id)
                detected_lang = language_info.get('primary_language')

                if detected_lang and detected_lang in available_languages:
                    # Check confidence score
                    confidence_scores = language_info.get('confidence_scores', {})
                    script = language_info.get('script')
                    if script and confidence_scores.get(script, 0) > 0.6:
                        logger.info(f"Language detected via script analysis: {detected_lang} (confidence: {confidence_scores.get(script, 0):.2f})")
                        return detected_lang

                # Fallback to simple script detection
                fallback_lang = detect_language_by_script(speech_result)
                if fallback_lang and fallback_lang in available_languages:
                    logger.info(f"Language detected via fallback script detection: {fallback_lang}")
                    return fallback_lang

                # Keyword matching with enhanced patterns
                detected_keyword_lang = self._detect_language_by_keywords(speech_result, available_languages)
                if detected_keyword_lang:
                    return detected_keyword_lang

            except Exception as e:
                logger.error(f"Error in enhanced language detection: {e}")

        return default_language

    def _detect_language_by_keywords(self, speech_result: str, available_languages: List[str]) -> Optional[str]:
        """Detect language using keyword matching"""
        speech_lower = speech_result.lower()

        # Use centralized language keywords to avoid duplication
        language_keywords = language_config.get_language_keywords()

        for keyword, code in language_keywords.items():
            if keyword in speech_lower and code in available_languages:
                logger.info(f"Language detected via keyword matching: {code} (keyword: {keyword})")
                return code

        return None

    def _determine_language_selection(self, speech_result: str, dtmf_digits: str,
                                    available_languages: List[str]) -> str:
        """Determine selected language from input"""
        default_language = get_primary_language()
        if default_language not in available_languages:
            default_language = available_languages[0] if available_languages else "hi"

        # Check DTMF first
        if dtmf_digits and dtmf_digits.isdigit():
            digit = int(dtmf_digits)
            if 1 <= digit <= len(available_languages):
                return available_languages[digit-1]

        # Check speech recognition
        if speech_result:
            detected_lang = detect_language(speech_result)
            if detected_lang in available_languages:
                return detected_lang

            # Keyword matching using centralized configuration
            speech_lower = speech_result.lower()
            language_keywords = language_config.get_language_keywords()

            for keyword, code in language_keywords.items():
                if keyword in speech_lower and code in available_languages:
                    return code

        return default_language

    def _determine_menu_selection(self, speech_result: str, dtmf_digits: str) -> int:
        """Determine menu selection from input"""
        # Check DTMF first
        if dtmf_digits and dtmf_digits.isdigit():
            return int(dtmf_digits)

        # Check speech keywords
        if speech_result:
            speech_lower = speech_result.lower()
            if any(word in speech_lower for word in ["doctor", "appointment", "डॉक्टर", "अपॉइंटमेंट"]):
                return 1
            elif any(word in speech_lower for word in ["test", "examination", "टेस्ट", "जांच"]):
                return 2

        return 0  # Invalid selection

    def _match_doctor_selection(self, speech_result: str, dtmf_digits: str,
                              doctors: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Match doctor selection from input using production-ready fuzzy matching.

        Uses the existing fuzzy matching infrastructure to provide accurate
        doctor selection based on speech input or DTMF digits.
        """
        if not doctors:
            return None

        try:
            # Use the production fuzzy matcher from fuzzy_matcher.py
            matched_doctor = match_doctor_by_name(
                speech=speech_result or "",
                dtmf=dtmf_digits or "",
                doctors=doctors,
                threshold=0.6  # 60% similarity threshold for voice input
            )

            if matched_doctor:
                logger.info(f"Successfully matched doctor: {matched_doctor.get('name', 'Unknown')} "
                           f"from input: speech='{speech_result}', dtmf='{dtmf_digits}'")
                return matched_doctor

            # If fuzzy matching fails, try the main.py implementation as fallback
            # This handles the numbered selection format used in main.py
            fallback_match = match_doctor_selection(
                speech=speech_result or "",
                dtmf=dtmf_digits or "",
                doctors=doctors
            )

            if fallback_match:
                logger.info(f"Fallback matched doctor: {fallback_match.get('name', 'Unknown')}")
                return fallback_match

            # Log the failed match for debugging
            logger.warning(f"No doctor match found for speech='{speech_result}', dtmf='{dtmf_digits}' "
                          f"among {len(doctors)} doctors")
            return None

        except Exception as e:
            logger.error(f"Error in doctor matching: {e}")
            return None

    def _match_test_selection(self, speech_result: str, dtmf_digits: str,
                            tests: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Match test selection from input using production-ready fuzzy matching.

        Uses the existing fuzzy matching infrastructure to provide accurate
        test selection based on speech input or DTMF digits.
        """
        if not tests:
            return None

        try:
            # Use the production fuzzy matcher from fuzzy_matcher.py
            matched_test = match_test_by_name(
                speech=speech_result or "",
                dtmf=dtmf_digits or "",
                tests=tests,
                threshold=0.6  # 60% similarity threshold for voice input
            )

            if matched_test:
                logger.info(f"Successfully matched test: {matched_test.get('name', 'Unknown')} "
                           f"from input: speech='{speech_result}', dtmf='{dtmf_digits}'")
                return matched_test

            # If fuzzy matching fails, try the main.py implementation as fallback
            # This handles the numbered selection format used in main.py
            fallback_match = match_test_selection(
                speech=speech_result or "",
                dtmf=dtmf_digits or "",
                tests=tests
            )

            if fallback_match:
                logger.info(f"Fallback matched test: {fallback_match.get('name', 'Unknown')}")
                return fallback_match

            # Log the failed match for debugging
            logger.warning(f"No test match found for speech='{speech_result}', dtmf='{dtmf_digits}' "
                          f"among {len(tests)} tests")
            return None

        except Exception as e:
            logger.error(f"Error in test matching: {e}")
            return None

    def _match_time_selection(self, speech_result: str, dtmf_digits: str,
                            time_slots: List[str]) -> Optional[str]:
        """
        Match time selection from input using production-ready fuzzy matching.

        Uses the existing fuzzy matching infrastructure to provide accurate
        time slot selection based on speech input or DTMF digits.
        """
        if not time_slots:
            return None

        try:
            # Use the production fuzzy matcher from fuzzy_matcher.py
            matched_time = match_time_slot(
                speech=speech_result or "",
                dtmf=dtmf_digits or "",
                time_slots=time_slots,
                threshold=0.7  # Higher threshold for time matching (70%)
            )

            if matched_time:
                logger.info(f"Successfully matched time slot: {matched_time} "
                           f"from input: speech='{speech_result}', dtmf='{dtmf_digits}'")
                return matched_time

            # If fuzzy matching fails, try the main.py implementation as fallback
            # This handles the numbered selection format used in main.py
            fallback_match = match_time_selection(
                speech=speech_result or "",
                dtmf=dtmf_digits or "",
                time_slots=time_slots
            )

            if fallback_match:
                logger.info(f"Fallback matched time slot: {fallback_match}")
                return fallback_match

            # Log the failed match for debugging
            logger.warning(f"No time slot match found for speech='{speech_result}', dtmf='{dtmf_digits}' "
                          f"among {len(time_slots)} slots")
            return None

        except Exception as e:
            logger.error(f"Error in time slot matching: {e}")
            return None

    def _determine_confirmation(self, speech_result: str, dtmf_digits: str) -> bool:
        """Determine confirmation from input"""
        if dtmf_digits:
            return dtmf_digits == "1"

        if speech_result:
            speech_lower = speech_result.lower()
            yes_words = ["yes", "confirm", "ok", "हां", "हाँ", "ठीक है"]
            return any(word in speech_lower for word in yes_words)

        return False

    async def _get_translated_text(self, text: str, language: str) -> str:
        """Get translated text (placeholder for actual translation)"""
        # This would integrate with the language_config system
        # For now, return as-is
        return text

    async def _get_available_doctors(self, hospital_id: str, date: str) -> List[Dict[str, Any]]:
        """Get available doctors for date"""
        try:
            return await get_doctors(hospital_id, force_refresh=False)
        except Exception as e:
            logger.error(f"Error getting doctors: {e}")
            return []

    async def _get_available_tests(self, hospital_id: str, date: str) -> List[Dict[str, Any]]:
        """Get available tests for date"""
        try:
            return await get_tests(hospital_id, force_refresh=False)
        except Exception as e:
            logger.error(f"Error getting tests: {e}")
            return []

    async def _format_doctor_options(self, doctors: List[Dict[str, Any]], language: str) -> str:
        """Format doctor options for speech"""
        if not doctors:
            return "No doctors available."

        options = []
        for i, doctor in enumerate(doctors[:5], 1):  # Limit to 5 options
            name = doctor.get("name", "Unknown")
            specialty = doctor.get("specialty", "General")
            options.append(f"{i}. Dr. {name}, {specialty}")

        return ". ".join(options)

    async def _format_test_options(self, tests: List[Dict[str, Any]], language: str) -> str:
        """Format test options for speech"""
        if not tests:
            return "No tests available."

        options = []
        for i, test in enumerate(tests[:5], 1):  # Limit to 5 options
            name = test.get("name", "Unknown")
            price = test.get("price", "Standard rate")
            options.append(f"{i}. {name}, {price}")

        return ". ".join(options)

    async def _format_time_slots(self, slots: List[str], language: str) -> str:
        """Format time slots for speech"""
        if not slots:
            return "No time slots available."

        options = []
        for i, slot in enumerate(slots[:5], 1):  # Limit to 5 options
            options.append(f"{i}. {slot}")

        return ". ".join(options)

    async def _get_available_time_slots(self, doctor: Dict[str, Any], hospital_id: str) -> List[str]:
        """Get available time slots for doctor using dynamic scheduling"""
        try:
            doctor_id = doctor.get('id')
            if not doctor_id:
                logger.warning("Doctor ID not found in doctor data")
                return []

            # Get today's date for appointment scheduling
            today = datetime.now().strftime('%Y-%m-%d')

            # Use the appointment scheduler to get real time slots
            time_slots = await appointment_scheduler.get_available_time_slots(
                hospital_id=hospital_id,
                doctor_id=doctor_id,
                date=today
            )

            logger.info(f"Retrieved {len(time_slots)} available time slots for doctor {doctor_id}")
            return time_slots

        except Exception as e:
            logger.error(f"Error getting available time slots for doctor {doctor.get('id', 'unknown')}: {e}")
            # Fallback to basic time slots if the dynamic system fails
            return ["9:00 AM", "10:00 AM", "11:00 AM", "2:00 PM", "3:00 PM"]

    async def _get_available_test_slots(self, hospital_id: str, test_id: str) -> List[str]:
        """Get available time slots for test using dynamic scheduling"""
        try:
            if not test_id:
                logger.warning("Test ID not provided")
                return []

            # Get today's date for test scheduling
            today = datetime.now().strftime('%Y-%m-%d')

            # Use the appointment scheduler to get real test time slots
            time_slots = await appointment_scheduler.get_available_test_slots(
                hospital_id=hospital_id,
                test_id=test_id,
                date=today
            )

            logger.info(f"Retrieved {len(time_slots)} available test slots for test {test_id}")
            return time_slots

        except Exception as e:
            logger.error(f"Error getting available test slots for test {test_id}: {e}")
            # Fallback to basic time slots if the dynamic system fails
            return ["9:00 AM", "10:30 AM", "12:00 PM", "2:30 PM", "4:00 PM"]

    async def _process_booking(self, ctx: CallContext, hospital_id: str) -> Dict[str, Any]:
        """
        Process the actual booking by creating appointments/test bookings in PostgreSQL.

        This production-ready implementation:
        1. Validates all required booking data
        2. Creates actual appointments/test bookings in hospital-specific PostgreSQL databases
        3. Handles booking limits and availability
        4. Returns real booking reference numbers
        5. Provides proper error handling and rollback
        """
        try:
            # Validate required booking data
            validation_result = await self._validate_booking_data(ctx, hospital_id)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": validation_result["error"]
                }

            # Extract validated booking data
            patient_name = ctx.data.get("patient_name")
            phone = ctx.caller_number
            appointment_type = ctx.data.get("appointment_type")
            doctor_id = ctx.data.get("doctor_id")
            test_id = ctx.data.get("test_id")
            appointment_time = ctx.data.get("appointment_time")

            # Validate phone number
            phone_validation = validate_phone_number(phone)
            if not phone_validation["valid"]:
                return {
                    "success": False,
                    "error": phone_validation["error"]
                }

            # Use standardized phone number
            phone = phone_validation.get("standardized", phone)

            # Format appointment time for database storage
            formatted_time = await self._format_appointment_time(appointment_time)
            if not formatted_time:
                return {
                    "success": False,
                    "error": "Invalid appointment time format"
                }

            # Process based on appointment type
            if appointment_type == "doctor":
                return await self._create_doctor_appointment(
                    hospital_id, patient_name, phone, doctor_id, formatted_time
                )
            elif appointment_type == "test":
                return await self._create_test_booking(
                    hospital_id, patient_name, phone, test_id, formatted_time
                )
            else:
                return {
                    "success": False,
                    "error": "Invalid appointment type"
                }

        except Exception as e:
            logger.error(f"Error processing booking for call {ctx.call_id}: {e}")
            return {
                "success": False,
                "error": "Booking system temporarily unavailable. Please try again later."
            }

    async def _validate_booking_data(self, ctx: CallContext, hospital_id: str) -> Dict[str, Any]:
        """
        Validate all required booking data is present and valid.

        Returns:
            Dict with 'valid' boolean and 'error' message if invalid
        """
        try:
            # Check required fields
            patient_name = ctx.data.get("patient_name")
            if not patient_name or not patient_name.strip():
                return {"valid": False, "error": "Patient name is required"}

            phone = ctx.caller_number
            if not phone:
                return {"valid": False, "error": "Phone number is required"}

            appointment_type = ctx.data.get("appointment_type")
            if appointment_type not in ["doctor", "test"]:
                return {"valid": False, "error": "Invalid appointment type"}

            appointment_time = ctx.data.get("appointment_time")
            if not appointment_time:
                return {"valid": False, "error": "Appointment time is required"}

            # Validate doctor appointment
            if appointment_type == "doctor":
                doctor_id = ctx.data.get("doctor_id")
                if not doctor_id:
                    return {"valid": False, "error": "Doctor selection is required"}

                # Verify doctor exists and is available
                doctors = await get_doctors(hospital_id)
                doctor = next((d for d in doctors if d.get("id") == doctor_id), None)
                if not doctor:
                    return {"valid": False, "error": "Selected doctor not found"}

            # Validate test appointment
            elif appointment_type == "test":
                test_id = ctx.data.get("test_id")
                if not test_id:
                    return {"valid": False, "error": "Test selection is required"}

                # Verify test exists
                tests = await get_tests(hospital_id)
                test = next((t for t in tests if t.get("id") == test_id), None)
                if not test:
                    return {"valid": False, "error": "Selected test not found"}

            return {"valid": True}

        except Exception as e:
            logger.error(f"Error validating booking data: {e}")
            return {"valid": False, "error": "Validation failed"}

    async def _format_appointment_time(self, appointment_time: str) -> Optional[str]:
        """
        Format appointment time for database storage.

        Args:
            appointment_time: Natural language time or formatted time

        Returns:
            PostgreSQL-compatible datetime string or None if invalid
        """
        try:
            return format_appointment_datetime(appointment_time)
        except ValueError as e:
            logger.error(f"Invalid time format '{appointment_time}': {e}")
            return None
        except Exception as e:
            logger.error(f"Error formatting appointment time '{appointment_time}': {e}")
            return None

    async def _create_doctor_appointment(self, hospital_id: str, patient_name: str,
                                       phone: str, doctor_id: str, appointment_time: str) -> Dict[str, Any]:
        """
        Create a doctor appointment in PostgreSQL database.

        Returns:
            Dict with success status and appointment details
        """
        try:
            # Create appointment in PostgreSQL
            appointment_id = await create_appointment(
                hospital_id=hospital_id,
                patient_name=patient_name.strip(),
                phone=phone,
                doctor_id=doctor_id,
                appointment_time=appointment_time
            )

            if appointment_id:
                # Generate user-friendly reference number
                reference = f"APT{appointment_id:06d}"

                logger.info(f"Successfully created doctor appointment {appointment_id} for {patient_name}")

                return {
                    "success": True,
                    "reference": reference,
                    "appointment_id": appointment_id,
                    "message": "Doctor appointment confirmed",
                    "type": "doctor_appointment"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to create appointment in database"
                }

        except Exception as e:
            logger.error(f"Error creating doctor appointment: {e}")

            # Check for specific error types
            error_message = str(e)
            if "booking not available" in error_message.lower():
                return {
                    "success": False,
                    "error": f"Appointment not available. {error_message}"
                }
            elif "limit exceeded" in error_message.lower():
                return {
                    "success": False,
                    "error": "Doctor's daily booking limit reached. Please try another date."
                }
            else:
                return {
                    "success": False,
                    "error": "Unable to book appointment. Please try again later."
                }

    async def _create_test_booking(self, hospital_id: str, patient_name: str,
                                 phone: str, test_id: str, booking_time: str) -> Dict[str, Any]:
        """
        Create a test booking in PostgreSQL database.

        Returns:
            Dict with success status and booking details
        """
        try:
            # Create test booking in PostgreSQL
            booking_id = await create_test_booking(
                hospital_id=hospital_id,
                patient_name=patient_name.strip(),
                phone=phone,
                test_type_id=test_id,
                booking_time=booking_time
            )

            if booking_id:
                # Generate user-friendly reference number
                reference = f"TST{booking_id:06d}"

                logger.info(f"Successfully created test booking {booking_id} for {patient_name}")

                return {
                    "success": True,
                    "reference": reference,
                    "booking_id": booking_id,
                    "message": "Test booking confirmed",
                    "type": "test_booking"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to create test booking in database"
                }

        except Exception as e:
            logger.error(f"Error creating test booking: {e}")
            return {
                "success": False,
                "error": "Unable to book test. Please try again later."
            }

    def _create_error_verbs(self, error_message: str) -> List[Dict[str, Any]]:
        """Create error response verbs"""
        return [{
            "verb": "say",
            "text": f"Sorry, {error_message}. Please try your call again later."
        }, {
            "verb": "hangup"
        }]

# Global state handler instance
state_handlers = WebSocketStateHandlers()
