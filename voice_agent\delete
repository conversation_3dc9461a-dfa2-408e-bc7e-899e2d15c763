INFO:voice_agent.semantic_integration:Date-aware Redis components loaded successfully
INFO:shared.llm_service:AsyncOpenAI client initialized successfully
INFO:shared.llm_service:Registered function: get_hospital_info
INFO:shared.llm_service:Registered function: get_available_doctors
INFO:shared.llm_service:Registered function: get_doctor_schedule
INFO:shared.llm_service:Registered function: get_available_tests
INFO:shared.llm_service:Registered function: book_appointment
INFO:shared.llm_service:Registered function: book_test
INFO:shared.llm_service:Set handler for function: get_hospital_info
INFO:shared.llm_service:Set handler for function: get_available_doctors
INFO:shared.llm_service:Set handler for function: get_doctor_schedule
INFO:shared.llm_service:Set handler for function: get_available_tests
INFO:shared.llm_service:Set handler for function: book_appointment
INFO:shared.llm_service:Set handler for function: book_test
INFO:voice_agent.llm_integration:Voice agent LLM integration setup completed
INFO:voice_agent.websocket_manager:Registered handler for message type: session:new
INFO:voice_agent.websocket_manager:Registered handler for message type: verb:hook
INFO:voice_agent.websocket_manager:Registered handler for message type: call:status
INFO:voice_agent.websocket_manager:Registered handler for message type: session:reconnect    
INFO:voice_agent.websocket_manager:Registered handler for message type: session:redirect     
INFO:voice_agent.websocket_manager:Registered handler for message type: jambonz:error        
INFO:voice_agent.websocket_server:Successfully registered 6 WebSocket message handlers: session:new, verb:hook, call:status, session:reconnect, session:redirect, jambonz:error
INFO:voice_agent.websocket_server:WebSocket manager now has handlers for: session:new, verb:hook, call:status, session:reconnect, session:redirect, jambonz:error
INFO:voice_agent.websocket_config:WebSocket configuration validated successfully
INFO:voice_agent.nlp:Initialized language support for: ['hi', 'bn', 'en']
INFO:     Started server process [11812]
INFO:     Waiting for application startup.
INFO:__main__:Starting Megha Voice Agent with WebSocket and Semantic Caching
INFO:__main__:Shared Redis connection successful
INFO:__main__:Testing semantic integration async Redis connections...
INFO:__main__:✅ Semantic integration async Redis pools are ready
INFO:__main__:Starting WebSocket server for Jambonz integration...
INFO:voice_agent.websocket_metrics:Started metrics collection with 60s interval
INFO:__main__:✅ WebSocket metrics collection started
INFO:websockets.server:server listening on 0.0.0.0:8765
INFO:voice_agent.websocket_manager:WebSocket server started on 0.0.0.0:8765
INFO:voice_agent.websocket_manager:Subprotocol: ws.jambonz.org
INFO:voice_agent.websocket_manager:Max connections: 1000
INFO:voice_agent.websocket_server:Jambonz WebSocket server started successfully
INFO:__main__:✅ Internal WebSocket server started successfully
INFO:__main__:WebSocket server running on 0.0.0.0:8765
INFO:__main__:FastAPI WebSocket proxy endpoint: /ws/jambonz/{hospital_id}
INFO:__main__:WebSocket subprotocol: ws.jambonz.org
INFO:__main__:Max concurrent connections: 1000
INFO:__main__:Started semantic cache warm-up in background task
INFO:voice_agent.booking_limit_scheduler:Booking limit scheduler started - will refresh daily at 12:00 PM
INFO:__main__:✅ Booking limit scheduler started - daily refresh at 12:00 PM
INFO:__main__:Starting semantic cache warm-up process...
INFO:voice_agent.booking_limit_scheduler:Next booking limit refresh scheduled for: 2025-07-11 12:00:00
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:__main__:Loaded 3 doctors for hospital 123
INFO:__main__:Loaded 4 tests for hospital 123
INFO:__main__:Successfully loaded hospital 123 with 3 doctors and 4 tests
INFO:__main__:Loaded 3 doctors for hospital **********
INFO:__main__:Loaded 0 tests for hospital **********
INFO:__main__:Successfully loaded hospital ********** with 3 doctors and 0 tests
INFO:__main__:Loaded 2 doctors for hospital 456
INFO:__main__:Loaded 4 tests for hospital 456
INFO:__main__:Successfully loaded hospital 456 with 2 doctors and 4 tests
INFO:__main__:Successfully loaded 3 hospitals for semantic cache
INFO:__main__:Loaded 3 hospitals, starting cache warm-up...
INFO:semantic_processor:Preloaded patterns for hospital 123
INFO:semantic_processor:Preloaded patterns for hospital 123
INFO:semantic_processor:Preloaded patterns for hospital 123
INFO:semantic_processor:Preloaded patterns for hospital **********
INFO:semantic_processor:Preloaded patterns for hospital **********
INFO:semantic_processor:Preloaded patterns for hospital **********
INFO:semantic_processor:Preloaded patterns for hospital 456
INFO:semantic_processor:Preloaded patterns for hospital 456
INFO:semantic_processor:Preloaded patterns for hospital 456
INFO:voice_agent.semantic_integration:Cache warm-up completed: 3/3 hospitals loaded
INFO:__main__:✅ Semantic cache warmed up successfully for 3 hospitals
INFO:__main__:Cache contains: 8 doctors, 8 tests across all hospitals
INFO:voice_agent.semantic_integration:Date-aware integration initialized for hospital 123    
INFO:__main__:✅ Date-aware integration initialized for hospital 123
INFO:voice_agent.semantic_integration:Date-aware integration initialized for hospital **********
INFO:__main__:✅ Date-aware integration initialized for hospital **********
INFO:voice_agent.semantic_integration:Date-aware integration initialized for hospital 456    
INFO:__main__:✅ Date-aware integration initialized for hospital 456
INFO:__main__:✅ Date-aware integration initialized for 3/3 hospitals
INFO:     54.92.175.189:0 - "WebSocket /ws/jambonz/**********" [accepted]
INFO:__main__:WebSocket proxy connection established for hospital: **********
INFO:     connection open
INFO:websockets.server:connection open
INFO:voice_agent.websocket_manager:WebSocket connection received with path: /ws/jambonz/**********
INFO:voice_agent.websocket_manager:New WebSocket connection: 8926f3ab-3076-447a-b7a5-a9146274192c from ('127.0.0.1', 61432) for hospital: **********
INFO:voice_agent.websocket_manager:Added connection 8926f3ab-3076-447a-b7a5-a9146274192c to pool
INFO:__main__:Connected to internal WebSocket server: ws://127.0.0.1:8765/ws/jambonz/**********
ERROR:voice_agent.websocket_server:Could not extract hospital ID from called number: None
INFO:voice_agent.call_context:Initialized CallContext object for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad not found in cache. Creating and saving new context with this ID.
INFO:voice_agent.call_context:Successfully saved context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Initialized CallContext object for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.websocket_call_context:Successfully created WebSocket context from existing base context for call b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.websocket_handlers:Using hospital ID from WebSocket connection: **********
INFO:voice_agent.call_context:Initialized CallContext object for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Successfully loaded and parsed context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Successfully loaded context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.main:Getting hospital configuration for hospital ID: **********
INFO:voice_agent.main:Looking for hospital document at hospitals/hospital_**********_data
INFO:voice_agent.main:Found hospital config at hospitals/hospital_**********_data
INFO:voice_agent.main:Hospital data: {'name': 'Community Health Clinic', 'ssh_tunnel': {'private_key_path': '/path/to/private_key.pem', 'user': 'ssh_user', 'host': 'ssh.communityclinic.org'}, 'address': '789 Wellness Road, Smalltown, State, 54321', 'languages': ['hi', 'bn', 'en'], 'db_connection_string': 'postgresql://hospital_user:hospitaldb@localhost:5432/hospital459', 'created_at': '2025-01-01T00:00:00Z', 'id': '**********', 'sip_trunk': {'sip_endpoint': 'sip.jambonz.org', 'provider': 'jambonz', 'auth_token': 'sample_token_789'}, 'settings': {'services': ['voice'], 'timezone': 'America/Chicago', 'logo_url': 'https://example.com/community_logo.png', 'appointment_duration_minutes': 30, 'working_hours': {'friday': '09:00-17:00', 'saturday': '10:00-14:00', 'sunday': 'closed', 'monday': '09:00-17:00', 'tuesday': '09:00-17:00', 'thursday': '09:00-17:00', 'wednesday': '09:00-17:00'}}, 'phone': '+17654321098', 'email': '<EMAIL>', 'emergency_number': '911'}
INFO:voice_agent.utils:No existing port allocation for hospital **********, using auto-allocation
INFO:voice_agent.utils:SSH tunnel binding to loopback interface (127.0.0.1) for security     
ERROR:voice_agent.utils:Error creating SSH tunnel: No password or public key available!
ERROR:voice_agent.main:Error creating SSH tunnel: No password or public key available!       
INFO:voice_agent.semantic_integration:Set language preferences for hospital **********: ['hi', 'bn', 'en']
INFO:voice_agent.websocket_handlers:Session started for call b5ee05f7-82a2-40cb-ab32-0964c9cc5aad, hospital **********
INFO:voice_agent.websocket_server:Successfully handled session:new for call b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.websocket_server:Call status update: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad -> None
INFO:voice_agent.websocket_handlers:Call status update: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad -> None
INFO:voice_agent.websocket_server:Call status update: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad -> None
INFO:voice_agent.websocket_handlers:Call status update: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad -> None
INFO:voice_agent.call_context:Initialized CallContext object for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Successfully loaded and parsed context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Successfully loaded context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
WARNING:voice_agent.websocket_handlers:Hospital ID is None in call context, attempting to get fallback
WARNING:voice_agent.websocket_handlers:Using hardcoded fallback hospital ID: 123
INFO:voice_agent.hospital_utils:Getting hospital configuration for hospital ID: 123
INFO:voice_agent.hospital_utils:Looking for hospital document at hospitals/hospital_123_data 
INFO:voice_agent.hospital_utils:Found hospital config at hospitals/hospital_123_data
INFO:voice_agent.hospital_utils:Hospital data: {'name': 'General Hospital', 'languages': ['en', 'hi', 'bn'], 'address': '123 Main Street, City, State, 12345', 'whatsapp': {'webhook_path': '/whatsapp/hospital123', 'phone_number': '+14155238886', 'provider': 'twilio'}, 'db_connection_string': 'postgresql://hospital_user:hospitaldb@localhost:5432/hospital123', 'created_at': '2025-01-01T00:00:00Z', 'id': '123', 'sip_trunk': {'sip_endpoint': 'sip.jambonz.org', 'provider': 'jambonz', 'auth_token': 'sample_token_123'}, 'settings': {'services': ['voice', 'whatsapp'], 'timezone': 'America/New_York', 'logo_url': 'https://example.com/hospital_logo.png', 'appointment_duration_minutes': 30, 'working_hours': {'friday': '08:00-20:00', 'saturday': '09:00-17:00', 'sunday': '09:00-17:00', 'monday': '08:00-20:00', 'tuesday': '08:00-20:00', 'thursday': '08:00-20:00', 'wednesday': '08:00-20:00'}}, 'phone': '+19876543210', 'email': '<EMAIL>', 'emergency_number': '911'}
INFO:voice_agent.hospital_utils:Successfully created HospitalConfig for hospital 123
INFO:voice_agent.call_context:Initialized CallContext object for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Successfully loaded and parsed context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
INFO:voice_agent.call_context:Successfully loaded context for call_id: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad
WARNING:voice_agent.websocket_handlers:Hospital ID is None in call context, attempting to get fallback
WARNING:voice_agent.websocket_handlers:Using hardcoded fallback hospital ID: 123
INFO:voice_agent.hospital_utils:Getting hospital configuration for hospital ID: 123
INFO:voice_agent.hospital_utils:Looking for hospital document at hospitals/hospital_123_data 
INFO:voice_agent.hospital_utils:Found hospital config at hospitals/hospital_123_data
INFO:voice_agent.hospital_utils:Hospital data: {'name': 'General Hospital', 'languages': ['en', 'hi', 'bn'], 'address': '123 Main Street, City, State, 12345', 'whatsapp': {'webhook_path': '/whatsapp/hospital123', 'phone_number': '+14155238886', 'provider': 'twilio'}, 'db_connection_string': 'postgresql://hospital_user:hospitaldb@localhost:5432/hospital123', 'created_at': '2025-01-01T00:00:00Z', 'id': '123', 'sip_trunk': {'sip_endpoint': 'sip.jambonz.org', 'provider': 'jambonz', 'auth_token': 'sample_token_123'}, 'settings': {'services': ['voice', 'whatsapp'], 'timezone': 'America/New_York', 'logo_url': 'https://example.com/hospital_logo.png', 'appointment_duration_minutes': 30, 'working_hours': {'friday': '08:00-20:00', 'saturday': '09:00-17:00', 'sunday': '09:00-17:00', 'monday': '08:00-20:00', 'tuesday': '08:00-20:00', 'thursday': '08:00-20:00', 'wednesday': '08:00-20:00'}}, 'phone': '+19876543210', 'email': '<EMAIL>', 'emergency_number': '911'}
INFO:voice_agent.hospital_utils:Successfully created HospitalConfig for hospital 123
INFO:voice_agent.websocket_server:Call status update: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad -> None
INFO:voice_agent.websocket_handlers:Call status update: b5ee05f7-82a2-40cb-ab32-0964c9cc5aad -> None
INFO:__main__:Jambonz WebSocket disconnected for hospital: **********
INFO:     connection closed
WARNING:voice_agent.websocket_manager:Stale connection detected: 8926f3ab-3076-447a-b7a5-a9146274192c
INFO:voice_agent.websocket_manager:Removed connection 8926f3ab-3076-447a-b7a5-a9146274192c from pool
INFO:voice_agent.websocket_manager:Closed connection 8926f3ab-3076-447a-b7a5-a9146274192c: Stale connection