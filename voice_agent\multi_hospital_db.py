import os
import json
import asyncio
import logging
import os
import threading
from typing import Dict, Any, List, Optional, Tuple, Union, Set
import ssl
import socket
from urllib.parse import urlparse

import asyncpg
from asyncpg.exceptions import PostgresError
from ssh2.session import Session
from ssh2.channel import Channel

from .utils import port_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("multi_hospital_db")

class DatabaseConnection:
    """Database connection configuration and state for a hospital."""
    
    def __init__(self, hospital_id: str, config: Dict[str, Any]):
        self.hospital_id: str = hospital_id
        self.host: str = config.get("host")
        self.port: int = config.get("port", 5432)
        self.database: str = config.get("database")
        self.user: str = config.get("user")
        self.password: str = config.get("password")
        self.ssl: bool = config.get("ssl", False)
        self.ssh_tunnel: Dict[str, Any] = config.get("ssh_tunnel")
        
        self.pool: Optional[asyncpg.Pool] = None
        self.ssh_session: Optional[Session] = None
        self.local_socket: Optional[socket.socket] = None
        self.raw_socket: Optional[socket.socket] = None  # SSH connection socket
        self.local_port: Optional[int] = None
        
    async def connect(self) -> bool:
        """
        Establish database connection pool for this hospital.
        Returns True if connection is successful, False otherwise.
        """
        try:
            # If SSH tunnel is required
            if self.ssh_tunnel:
                await self._establish_ssh_tunnel()
                
                # Use local port via SSH tunnel
                connection_params = {
                    "host": "localhost",
                    "port": self.local_port,
                    "database": self.database,
                    "user": self.user,
                    "password": self.password,
                }
            else:
                # Direct connection
                connection_params = {
                    "host": self.host,
                    "port": self.port,
                    "database": self.database,
                    "user": self.user,
                    "password": self.password,
                }
            
            # Add SSL if enabled
            if self.ssl:
                # `True` lets asyncpg create its own default SSLContext
                connection_params["ssl"] = True
            
            # Create connection pool
            self.pool = await asyncpg.create_pool(
                **connection_params,
                min_size=2,
                max_size=10,
                command_timeout=60,
                max_inactive_connection_lifetime=300
            )
            
            logger.info(f"Connected to database for hospital {self.hospital_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database for hospital {self.hospital_id}: {str(e)}")
            return False
    
    async def _establish_ssh_tunnel(self):
        """
        Establish SSH tunnel for database connection using robust port management.
        For testing: bypass SSH tunnel if BYPASS_SSH_TUNNEL environment variable is set.
        """
        # Check for bypass flag for testing
        bypass_ssh = os.getenv('BYPASS_SSH_TUNNEL', '').lower() in ('true', '1', 'yes')
        if bypass_ssh:
            logger.info(f"SSH tunnel bypassed for testing (hospital {self.hospital_id})")
            return

        try:
            # Use port manager for robust port allocation
            try:
                self.local_port = port_manager.allocate_port(self.hospital_id, "database_tunnel")
            except Exception as e:
                logger.error(f"Failed to allocate port for hospital {self.hospital_id}: {e}")
                raise

            # Create socket with allocated port
            self.local_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.local_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.local_socket.bind(("localhost", self.local_port))
            
            # SSH connection
            self.raw_socket = socket.create_connection(
                (self.ssh_tunnel["host"], self.ssh_tunnel.get("port", 22))
            )
            self.ssh_session = Session()
            self.ssh_session.handshake(self.raw_socket)
            
            # Authenticate
            if "private_key_path" in self.ssh_tunnel:
                self.ssh_session.userauth_publickey_fromfile(
                    self.ssh_tunnel["user"],
                    self.ssh_tunnel["private_key_path"]
                )
            elif "password" in self.ssh_tunnel:
                self.ssh_session.userauth_password(
                    self.ssh_tunnel["user"],
                    self.ssh_tunnel["password"]
                )
            else:
                raise ValueError("SSH tunnel requires either private_key_path or password")
            
            # Create channel for port forwarding
            remote_host = self.ssh_tunnel.get("remote_host", self.host)
            remote_port = self.ssh_tunnel.get("remote_port", self.port)
            
            # Start listening
            self.local_socket.listen(1)
            
            # Handle each client connection in a separate thread
            def handle_client(client_socket):
                """Handle a single client connection with its own SSH channel."""
                channel = None
                try:
                    # New channel for each client to prevent deadlocks
                    channel = self.ssh_session.channel_direct_tcpip(
                        remote_host,
                        remote_port,
                        "localhost",
                        self.local_port,
                    )

                    # Forward local connection to SSH channel
                    while True:
                        data = client_socket.recv(1024)
                        if not data:
                            break
                        channel.write(data)

                        # Get response from remote server
                        response = channel.read(1024)
                        if response:
                            client_socket.send(response)

                except Exception as e:
                    logger.error(f"Error in client handler for hospital {self.hospital_id}: {e}")
                finally:
                    # Clean up resources
                    try:
                        client_socket.close()
                    except Exception:
                        pass

                    if channel:
                        try:
                            channel.close()
                        except Exception as e:
                            logger.debug(f"Error closing SSH channel: {e}")

            # Main forwarding thread that accepts connections
            def forward_thread():
                while True:
                    try:
                        client, _ = self.local_socket.accept()
                        # Handle each client in a separate daemon thread
                        client_thread = threading.Thread(
                            target=handle_client,
                            args=(client,),
                            daemon=True
                        )
                        client_thread.start()
                    except Exception as e:
                        logger.error(f"Error accepting client connection for hospital {self.hospital_id}: {e}")
                        break

            # Start forwarding thread
            forwarding_thread = threading.Thread(target=forward_thread, daemon=True)
            forwarding_thread.start()
            
            logger.info(f"Established SSH tunnel for hospital {self.hospital_id}")
            
        except Exception as e:
            logger.error(f"Failed to establish SSH tunnel for hospital {self.hospital_id}: {str(e)}")
            raise
    
    async def close(self):
        """
        Close database connection, SSH tunnel, and release allocated ports.
        """
        try:
            if self.pool:
                await self.pool.close()
                self.pool = None

            if self.local_socket:
                self.local_socket.close()
                self.local_socket = None

            if self.ssh_session:
                self.ssh_session.disconnect()
                self.ssh_session = None

            if self.raw_socket:
                self.raw_socket.close()
                self.raw_socket = None


            # Release allocated port with error handling
            if self.local_port:
                try:
                    port_manager.release_port(self.hospital_id, "database_tunnel")
                except Exception as port_exc:
                    logger.error(f"Error releasing port for hospital {self.hospital_id}: {str(port_exc)}")
                self.local_port = None

            logger.info(f"Closed database connection for hospital {self.hospital_id}")

        except Exception as e:
            logger.error(f"Error closing database connection for hospital {self.hospital_id}: {str(e)}")
    
    async def execute_query(self, query: str, *args) -> Optional[List[Dict[str, Any]]]:
        """
        Execute query on hospital's database.
        Returns list of records or None if error occurs.
        """
        if not self.pool:
            logger.error(f"No database connection for hospital {self.hospital_id}")
            return None
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.fetch(query, *args)
                return [dict(r) for r in result]
                
        except Exception as e:
            logger.error(f"Error executing query for hospital {self.hospital_id}: {str(e)}")
            return None

class MultiHospitalDBManager:
    """
    Manages database connections for multiple hospitals.
    Provides methods for dynamically routing queries to the correct database.
    """
    
    def __init__(self):
        self.connections: Dict[str, DatabaseConnection] = {}
        self.default_connection: Optional[DatabaseConnection] = None
        self.config_file: Optional[str] = None
    
    def _parse_database_url(self, url: str) -> Dict[str, Any]:
        """
        Parse DATABASE_URL environment variable into connection parameters.
        """
        parsed = urlparse(url)
        return {
            "host": parsed.hostname,
            "port": parsed.port or 5432,
            "database": parsed.path[1:],  # Remove leading slash
            "user": parsed.username,
            "password": parsed.password
        }
    
    async def initialize(self, config_file: Optional[str] = None):
        """
        Initialize database connections from configuration file or environment variables.
        
        Args:
            config_file: Path to hospital configuration JSON file
        """
        self.config_file = config_file
        
        try:
            # Check for config file
            if config_file and os.path.exists(config_file):
                await self._initialize_from_config(config_file)
            else:
                # Use environment variable
                await self._initialize_from_env()
                
            logger.info(f"Initialized multi-hospital database manager with {len(self.connections)} connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize multi-hospital database manager: {str(e)}")
            raise
    
    async def _initialize_from_config(self, config_file: str):
        """
        Initialize from configuration file.
        """
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Create connections for each hospital
        for hospital in config.get("hospitals", []):
            hospital_id = hospital.get("id")
            db_config = hospital.get("database", {})
            
            if hospital_id and db_config:
                connection = DatabaseConnection(hospital_id, db_config)
                self.connections[hospital_id] = connection
                
                # Set first connection as default
                if not self.default_connection:
                    self.default_connection = connection
    
    async def _initialize_from_env(self):
        """
        Initialize from DATABASE_URL environment variable for a single hospital.
        """
        database_url = os.environ.get("DATABASE_URL")
        if not database_url:
            raise ValueError("DATABASE_URL environment variable not set")
        
        # Parse DATABASE_URL
        db_config = self._parse_database_url(database_url)
        
        # Create a default connection
        default_hospital_id = os.environ.get("DEFAULT_HOSPITAL_ID", "default")
        connection = DatabaseConnection(default_hospital_id, db_config)
        
        self.connections[default_hospital_id] = connection
        self.default_connection = connection
    
    async def connect_all(self) -> Dict[str, bool]:
        """
        Connect to all hospital databases.
        Returns a dictionary of hospital_id to connection success boolean.
        """
        results = {}
        for hospital_id, connection in self.connections.items():
            try:
                success = await connection.connect()
                results[hospital_id] = success
            except Exception as e:
                logger.error(f"Error connecting to hospital {hospital_id}: {str(e)}")
                results[hospital_id] = False
        
        return results
    
    async def close_all(self):
        """
        Close all database connections.
        """
        for connection in self.connections.values():
            await connection.close()
        
        self.connections = {}
        self.default_connection = None
    
    async def get_connection(self, hospital_id: str) -> Optional[DatabaseConnection]:
        """
        Get database connection for a specific hospital.
        Returns None if the hospital is not found.
        """
        connection = self.connections.get(hospital_id)
        
        # Create connection if it doesn't exist
        if not connection and self.config_file and os.path.exists(self.config_file):
            # Look for hospital in config file
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            # Find hospital in config
            for hospital in config.get("hospitals", []):
                if hospital.get("id") == hospital_id:
                    db_config = hospital.get("database", {})
                    connection = DatabaseConnection(hospital_id, db_config)
                    success = await connection.connect()
                    
                    if success:
                        self.connections[hospital_id] = connection
                        logger.info(f"Dynamically created connection for hospital {hospital_id}")
                    else:
                        logger.error(f"Failed to dynamically create connection for hospital {hospital_id}")
                        return None
                    
                    break
        
        return connection
    
    async def execute_query(self, hospital_id: str, query: str, *args) -> Optional[List[Dict[str, Any]]]:
        """
        Execute query on a specific hospital's database.
        Returns list of records or None if error occurs.
        """
        connection = await self.get_connection(hospital_id)
        
        if not connection:
            if self.default_connection:
                logger.warning(f"Hospital {hospital_id} not found, using default connection")
                connection = self.default_connection
            else:
                logger.error(f"Hospital {hospital_id} not found and no default connection available")
                return None
        
        return await connection.execute_query(query, *args)
    
    async def execute_transaction(self, hospital_id: str, queries: List[Tuple[str, List[Any]]]) -> bool:
        """
        Execute multiple queries as a transaction on a specific hospital's database.
        Returns True if transaction succeeds, False otherwise.
        """
        connection = await self.get_connection(hospital_id)
        
        if not connection or not connection.pool:
            logger.error(f"No database connection for hospital {hospital_id}")
            return False
        
        try:
            async with connection.pool.acquire() as conn:
                async with conn.transaction():
                    for query, args in queries:
                        await conn.execute(query, *args)
            
            return True
            
        except Exception as e:
            logger.error(f"Transaction failed for hospital {hospital_id}: {str(e)}")
            return False
    
    async def test_connection(self, hospital_id: str) -> Dict[str, Any]:
        """
        Test database connection for a specific hospital.
        Returns a dict with connection status details.
        """
        connection = await self.get_connection(hospital_id)
        
        if not connection:
            return {
                "success": False,
                "message": f"Hospital {hospital_id} not found"
            }
        
        if not connection.pool:
            return {
                "success": False,
                "message": f"No active connection for hospital {hospital_id}"
            }
        
        try:
            # Execute simple query to verify connection
            result = await connection.execute_query("SELECT 1 as test")
            
            if result and len(result) > 0 and result[0].get("test") == 1:
                # Get more connection details
                details = await connection.execute_query("""
                    SELECT 
                        current_database() as database,
                        current_user as user,
                        pg_backend_pid() as pid,
                        version() as version
                """)
                
                return {
                    "success": True,
                    "details": details[0] if details else {}
                }
            else:
                return {
                    "success": False,
                    "message": "Connection test failed"
                }
            
        except Exception as e:
            return {
                "success": False,
                "message": str(e)
            }
    
    async def list_hospitals(self) -> List[str]:
        """
        Get list of configured hospital IDs.
        """
        return list(self.connections.keys())

# Global instance of MultiHospitalDBManager
hospital_db_manager = MultiHospitalDBManager()

# Helper functions for external use

async def initialize_hospital_databases(config_file: str = None) -> Dict[str, bool]:
    """
    Initialize hospital databases from configuration file.
    Returns a dictionary of hospital_id to connection success boolean.
    """
    await hospital_db_manager.initialize(config_file)
    return await hospital_db_manager.connect_all()

async def execute_query(hospital_id: str, query: str, *args) -> Optional[List[Dict[str, Any]]]:
    """
    Execute query on a specific hospital's database.
    Returns list of records or None if error occurs.
    """
    return await hospital_db_manager.execute_query(hospital_id, query, *args)

async def execute_transaction(hospital_id: str, queries: List[Tuple[str, List[Any]]]) -> bool:
    """
    Execute multiple queries as a transaction.
    Returns True if transaction succeeds, False otherwise.
    """
    return await hospital_db_manager.execute_transaction(hospital_id, queries)

async def close_all_connections():
    """
    Close all database connections.
    """
    await hospital_db_manager.close_all()

class HospitalDBContextManager:
    """
    Context manager for hospital database connection.
    Allows for easy use in 'async with' statements.
    """
    
    def __init__(self, hospital_id: str):
        self.hospital_id = hospital_id
        self.connection = None
        self.conn = None
    
    async def __aenter__(self):
        self.connection = await hospital_db_manager.get_connection(self.hospital_id)
        
        if not self.connection or not self.connection.pool:
            raise ValueError(f"No database connection for hospital {self.hospital_id}")
        
        self.conn = await self.connection.pool.acquire()
        return self.conn
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # exc_tb (traceback) is intentionally unused - we only log the exception type and value
        if self.conn:
            try:
                await self.connection.pool.release(self.conn)
            except Exception as e:
                logger.error(f"Error releasing connection for hospital {self.hospital_id}: {e}")

        # Return None to propagate any exception that occurred in the with block
        if exc_type:
            logger.error(f"Exception in hospital DB context for {self.hospital_id}: {exc_type.__name__}: {exc_val}")
        return None

async def with_hospital_db(hospital_id: str) -> HospitalDBContextManager:
    """
    Get a context manager for a hospital database connection.
    Usage:
        async with await with_hospital_db("hospital1") as conn:
            result = await conn.fetch("SELECT * FROM users")
    """
    return HospitalDBContextManager(hospital_id)

# Initialize hospital databases if this file is run directly
if __name__ == "__main__":
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="Multi-hospital database manager")
    parser.add_argument("--config", help="Path to hospital configuration JSON file")
    parser.add_argument("--test", help="Test connection for specified hospital ID", action="store_true")
    parser.add_argument("--hospital", help="Hospital ID to test")
    
    args = parser.parse_args()
    
    async def main():
        if args.config:
            result = await initialize_hospital_databases(args.config)
            print(f"Initialization result: {json.dumps(result, indent=2)}")
            
            if args.test and args.hospital:
                test_result = await hospital_db_manager.test_connection(args.hospital)
                print(f"Connection test for {args.hospital}: {json.dumps(test_result, indent=2)}")
        else:
            print("No configuration file specified. Use --config to specify a configuration file.")
            sys.exit(1)
        
        await close_all_connections()
    
    asyncio.run(main())